import Container from 'react-bootstrap/Container';
import Image from "next/image";

const MagicRecipe = () => {
    return (
        <section className='section company-magicrecipe-wrap'>
            <Container fluid={"lg"}>
                <div className="section-head text-center mb-lg-5 mb-0">
                    <h2 className="main-title mb-2">
                        Our magic recipe
                    </h2>
                    <p>The three attributes that we believe have made us one of the leading technology companies in the USA are:</p>
                </div>
                <div className='magic-recipe-row d-flex'>
                    <div className='magic-recipe-cell magic-recipe-cell-gray'>
                        <div className='magic-recipe-cell-cont'>
                            <Image src="/images/p-service.png" alt='magic recipe' width="235" height="144" />
                            <div className='magic-recipe-content-wrap mt-3'>
                                <h4>Innovation at the Core</h4>
                                <p>Our commitment to staying at the forefront of technology and constantly improving our products allows us to deliver solutions that meet evolving industry needs.</p>
                            </div>
                        </div>
                    </div>
                    <div className='magic-recipe-cell magic-recipe-cell-black'>
                        <div className='magic-recipe-cell-cont'>
                            <Image src="/images/em-culture.png" alt='magic recipe' width="235" height="165" />
                            <div className='magic-recipe-content-wrap mt-3'>
                                <h4>Customer-Centric 
                                Approach</h4>
                                <p>We build every solution with the user in mind, ensuring each product is easy to use, highly functional, and addresses real customer challenges.</p>
                            </div>
                        </div>
                    </div>
                    <div className='magic-recipe-cell magic-recipe-cell-secondary'>
                        <div className='magic-recipe-cell-cont'>
                            <Image src="/images/talntd-team.png" alt='magic recipe' width="223" height="152" />
                            <div className='magic-recipe-content-wrap mt-3'>
                                <h4>Agility and Adaptability</h4>
                                <p>Our ability to quickly adapt to changes in the market and rapidly implement new solutions helps us stay ahead and serve our clients effectively.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default MagicRecipe;
