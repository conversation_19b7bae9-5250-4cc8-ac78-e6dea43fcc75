"use client";
import { Form } from "react-bootstrap";
import { getBlogSearch, getCarouselBlog } from "@/lib/actions";
import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";

import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const BlogSearch = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [dataBlog, setDataBlog] = useState([]);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSearch = async (term) => {
    setSearchTerm(term);
    if (term.trim() === "") {
      setSuggestions([]);
      setLoading(false);
      return;
    }
    setLoading(true);
    const data = await getBlogSearch(term);
    setSuggestions(data.results);
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    const dataBog = await getCarouselBlog(1, 10000);
    const uniqueCategories = Array.from(
      new Map(
        dataBog.results.map((blog) => [blog.category.id, blog.category])
      ).values()
    );
    setDataBlog(uniqueCategories);
  };

  const handleCategoryChange = (e) => {
    const selectedCategory = e.target.value;
    if (selectedCategory) {
      router.push(`/blog/category/${selectedCategory}`);
    }
  };

  return (
    <div className="blog-search">
      <div className="blog-search-row">
        <h4 className="mb-3">Search Blogs</h4>
        <Form.Group className="blog-form-item p-relative">
          <Form.Control
            type="text"
            required
            placeholder="Search"
            onChange={(e) => handleSearch(e.target.value)}
          />
          <button className="blog-search-icon select-arrow">
            <svg
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16.6 18L10.3 11.7C9.8 12.1 9.225 12.4167 8.575 12.65C7.925 12.8833 7.23333 13 6.5 13C4.68333 13 3.14583 12.3708 1.8875 11.1125C0.629167 9.85417 0 8.31667 0 6.5C0 4.68333 0.629167 3.14583 1.8875 1.8875C3.14583 0.629167 4.68333 0 6.5 0C8.31667 0 9.85417 0.629167 11.1125 1.8875C12.3708 3.14583 13 4.68333 13 6.5C13 7.23333 12.8833 7.925 12.65 8.575C12.4167 9.225 12.1 9.8 11.7 10.3L18 16.6L16.6 18ZM6.5 11C7.75 11 8.8125 10.5625 9.6875 9.6875C10.5625 8.8125 11 7.75 11 6.5C11 5.25 10.5625 4.1875 9.6875 3.3125C8.8125 2.4375 7.75 2 6.5 2C5.25 2 4.1875 2.4375 3.3125 3.3125C2.4375 4.1875 2 5.25 2 6.5C2 7.75 2.4375 8.8125 3.3125 9.6875C4.1875 10.5625 5.25 11 6.5 11Z"
                fill="black"
              />
            </svg>
          </button>
          {searchTerm && (
            <div className="blog-search-suggestions-wrap">
              {loading ? (
                <ul className="blog-search-suggestions">
                  {[...Array(3)].map((_, index) => (
                    <li key={index}>
                      <div className="d-flex align-items-center justify-content-start gap-3 w-100">
                        <div className="suggestion-link w-100">
                          <span className="blog-search-img">
                            <Skeleton width="72" height="72" />
                          </span>
                          <Skeleton count={1.5} />
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : suggestions.length > 0 ? (
                <ul className="blog-search-suggestions">
                  {suggestions.map((suggestion) => (
                    <li key={suggestion.id}>
                      <Link
                        href={`/blog/${suggestion.url}`}
                        className="suggestion-link"
                      >
                        <div className="d-flex align-items-center justify-content-start gap-3">
                          <span className="blog-search-img">
                            <Image
                              src={suggestion?.image}
                              alt={suggestion.title}
                              width="72"
                              height="72"
                              style={{ height: "auto" }}
                            />
                          </span>
                          {suggestion.title}
                        </div>
                      </Link>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="no-results">
                  <Image
                    src="/images/no-result.png"
                    alt="No results"
                    width="150"
                    height="100"
                    style={{ height: "auto" }}
                  />
                  <p>No Results Found</p>
                </div>
              )}
            </div>
          )}
        </Form.Group>
      </div>
      <div className="blog-search-row ">
        <h4 className="mb-3">Categories</h4>
        <Form.Group className="blog-form-item p-relative">
          <Form.Select required onChange={handleCategoryChange}>
            <option value="">Choose</option>
            {dataBlog.map((item) => (
              <option key={item.id} value={item.slug}>
                {item.title}
              </option>
            ))}
          </Form.Select>
          <span className="select-arrow">
            <svg
              width="16"
              height="11"
              viewBox="0 0 16 11"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8 10.5332L0 2.53317L1.86667 0.666504L8 6.79984L14.1333 0.666504L16 2.53317L8 10.5332Z"
                fill="black"
              />
            </svg>
          </span>
        </Form.Group>
      </div>
    </div>
  );
};

export default BlogSearch;
