import React from 'react';
import { Row,  Button, Container } from 'react-bootstrap';
import Image from 'next/image';

const CaseStudyItem = ({ index, image, title, description, }) => {
    const isOdd = index % 2 !== 0;

    return (
        <div className="case-study-section align-items-center">
            {isOdd ? (
                <>
                    <Container>
                        <Row className='justify-content-between align-items-center case-study-row'>
                            <div className='case-study-left'>
                                <div className='casestudy-cell-1'>
                                    <div className='casestudy-img-wrap'>
                                        <Image src={image} alt='casestudy project' width="416" height="457" />
                                    </div>
                                </div>
                            </div>
                            <div className='case-study-right  d-flex justify-content-end'>
                                <div className='casestudy-cell-2'>
                                    <div className="casestudy-content">
                                        <h3>{title}</h3>
                                        <p className='mb-40'>{description}</p>
                                        <div className="d-flex align-items-center casestudy-footer">
                                            <Button className='btn-white border-btn'>Case Study</Button>
                                            <div className="rating">
                                                <Image src='/images/clutch-rating-logo.png' alt='clutch' width="195" height="17" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Row>
                    </Container>
                </>
            ) : (
                <>
                    <Container>
                        <Row className='justify-content-between align-items-center'>
                            <div className='case-study-left'>
                                <div className='casestudy-cell-2'>
                                    <div className="casestudy-content">
                                        <h3>{title}</h3>
                                        <p className='mb-40'>{description}</p>
                                        <div className="d-flex align-items-center casestudy-footer">
                                            <Button className='btn-white border-btn'>Case Study</Button>
                                            <div className="rating">
                                                <Image src='/images/clutch-rating-logo.png' alt='clutch' width="195" height="17" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className='case-study-right d-flex justify-content-end'>
                                <div className='casestudy-cell-1'>
                                    <div className='casestudy-img-wrap'>
                                        <Image src={image} alt='casestudy project' width="487" height="542" />
                                    </div>
                                </div>
                            </div>
                        </Row>
                    </Container>
                </>
            )}
        </div>
    );
};

export default CaseStudyItem;
