import React from 'react';
import CompanyBanner from './_components/companybanner';
import Achievements from './_components/achievements';
import OurMission from './_components/mission';
import OurValues from './_components/ourvalues';
import PeopleBehind from './_components/peoplebehind';
import ClientSayCompany from './_components/clientsay';
import MagicRecipe from './_components/magicrecipe';
import { TalkToOurTeam } from '@/components/sections';

export const metadata = {
    metadataBase: new URL("https://becomap.com"),
    title: "Leading Indoor Mapping & Navigation Company| Becomap ",
    description:
      "Learn about Becom<PERSON>, a leader in indoor mapping and navigation solutions. Discover how we transform spaces with innovative technology and tailored services.",
    images: ["/becomap.png"],
    openGraph: {
      title: "Leading Indoor Mapping & Navigation Company| Becomap ",
      description:
        "<PERSON><PERSON> about <PERSON><PERSON><PERSON>, a leader in indoor mapping and navigation solutions. Discover how we transform spaces with innovative technology and tailored services.",
      images: ["/becomap.png"],
    },
  };

const Company = () => {
    return (
        <div className='company-wrap'>
            <CompanyBanner />
            <OurMission />
            <OurValues />
            <PeopleBehind />
            <Achievements />
            <ClientSayCompany />
            <MagicRecipe />
            <TalkToOurTeam source={"company"}/>
        </div>
    );
};

export default Company;
