"use client";
import { TalkToModal } from "@/components/ui";
import { useState } from "react";
import { <PERSON>ton, Col, Container, Row } from "react-bootstrap";

const IndustryBanner = ({ bgImage, title, description,source }) => {
  const [showModal, setShowModal] = useState(false);

  const openModal = () => {
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  return (
    <section
      className="industry-banner"
      style={{
        backgroundImage: `url(${bgImage?.src})`,
      }}
    >
      <Container fluid={"xl"}>
        <Row className="align-items-center">
          <Col md={6} className="w-1024-100">
            <div className="industry-banner-details">
              <h1
                className="main-title mb-2 mb-md-3"
                dangerouslySetInnerHTML={{ __html: title }}
              ></h1>
              <p dangerouslySetInnerHTML={{ __html: description }} />
              <div className="btn-wrap mt-3 mt-md-4">
                {/* eslint-disable react/no-unescaped-entities */}

                <Button
                  onClick={() => openModal()}
                  className="btn-secondary me-3"
                >
                  Let's talk to Sales
                </Button>
                {/* <Button className="btn btn-outline">See Demo</Button> */}
              </div>
            </div>
          </Col>
        </Row>
      </Container>
      <TalkToModal isOpen={showModal} onClose={() => closeModal()} hitUrl={source} />
    </section>
  );
};

export default IndustryBanner;
