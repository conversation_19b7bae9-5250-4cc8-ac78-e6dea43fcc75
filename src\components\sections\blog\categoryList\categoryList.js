"use client";
import { useState, useEffect } from "react";
import use<PERSON><PERSON> from "swr";
import axios from "axios";
import { Container, Row, Col } from "react-bootstrap";
import BlogPost from "../blogpost";
import { getCarouselBlog } from "@/lib/actions";
import Link from "next/link";
import "react-loading-skeleton/dist/skeleton.css";
import BlogLoader from "../blogLoader";

// Fetcher function for SWR with correct URL structure
const fetcher = (url) =>
  axios
    .get(url, {
      headers: {
        "X-Blog-Identifier": "beco",
      },
    })
    .then((res) => res.data);

const CategoryList = ({ slug, uniqueCategories }) => {
  const [pageSize, setPageSize] = useState(9); // Default page size
  const [page, setPage] = useState(1); // Current page number
  const [allPosts, setAllPosts] = useState([]); // Initialize with an empty array
  const [loadingMore, setLoadingMore] = useState(false); // Track loading state for "Read More"
  const [noMorePosts, setNoMorePosts] = useState(false); // Track if there are no more posts
  const [dataBlog, setDataBlog] = useState([]);
  const [loading, setLoading] = useState(true); // New loading state for skeletons

  // Construct URL with correct query params
  const categoryUrl = `https://blog-api.becomap.com/filter-by-category/?category=${slug}`;

  // Use SWR to fetch data with dynamic page size and page number
  const { data, error } = useSWR(categoryUrl, fetcher, {
    onSuccess: () => setLoading(false), // Set loading to false on data load
    onError: () => setLoading(false), // Handle loading state on error as well
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    const dataBog = await getCarouselBlog(1, 10000);
    const uniqueCategories = Array.from(
      new Map(
        dataBog.results.map((blog) => [blog.category.id, blog.category])
      ).values()
    );
    setDataBlog(uniqueCategories);
  };

  // if (error) return <Loading />;
  // if (!data && allPosts.length === 0) return <Loading />;

  return (
    <>
        <Container className="blog-container">
          <div className="section-head text-center category-title">
            <h2 className="main-title mb-4">
              <span className="secondary-color">#</span>
              {slug}
            </h2>
            <div className="category-title-top-row d-flex">
              {dataBlog.map((item) => (
                <Link href={`/blog/category/${item.slug}`} key={item.id || item.slug}>
                  <span>{item.title}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Blog Post Section with Skeleton Loaders */}
          <Row className="blog-post-smallbox">
            <Col md={12}>
              <Row>
                {loading
                  ? [1, 2, 3, 4, 5, 6, 7, 8, 9].map((key) => (
                      <Col md={4} key={key}>
                        <BlogLoader imageHeight={246} />
                      </Col>
                    ))
                  : data.results.map((item) => (
                      <Col md={4} key={item.id || item.url}>
                        <BlogPost item={item} imageHeight={246} />
                      </Col>
                    ))}
              </Row>
            </Col>
          </Row>
        </Container>
    </>
  );
};

export default CategoryList;
