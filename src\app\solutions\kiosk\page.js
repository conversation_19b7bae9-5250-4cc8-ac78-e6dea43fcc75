import SolutionBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
import IndustrySolutions from "../_components/solutions";
import FaqSection from "../_components/FaqSection";
import SolutionWhyUs from "../_components/solutionWhyUs";
import HeroImg from "@/assets/images/solutions/kiosk-hero.png";
import SecImg from "@/assets/images/solutions/becomap-care.png";
import { TalkToOurTeam } from "@/components/sections";
import Blogs from "@/app/home/<USER>/blogs";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

import SolutionWhyUsBg from "@/assets/images/solutions/why-bg.png";
import WhySubImage from "@/assets/images/solutions/kiosk/why-sub-img.png";
import SolutionsServices from "../_components/IndustryServices";


export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: " Enhance User Experience with Kiosk Wayfinder | Becomap",
  description:
    "Discover the power of Kiosk Wayfinder for seamless navigation with interactive kiosks. Enhance user experience with easy, intuitive wayfinding solutions.",
  images: ["/becomap.png"],
  openGraph: {
    title: " Enhance User Experience with Kiosk Wayfinder | Becomap",
    description:
      "Discover the power of Kiosk Wayfinder for seamless navigation with interactive kiosks. Enhance user experience with easy, intuitive wayfinding solutions.",
    images: ["/becomap.png"],
  },
};



const mallNavigationFAQ = [
  {
    question: "Can kiosks be integrated with my existing systems?",
    answer: "Yes, our kiosk solutions can be seamlessly integrated with your existing software, such as inventory management, CRM, payment systems, and more, ensuring a smooth and efficient workflow."
  },
  {
    question: "What are the key benefits of using kiosks in a business environment?",
    answer: "Kiosks improve operational efficiency, reduce wait times, enhance customer experience, increase sales, and allow for 24/7 service without the need for constant human supervision."
  },
  {
    question: "How long does it take to deploy a kiosk solution in my business?",
    answer: "Deployment time varies depending on the complexity and customization required, but typically, kiosks can be installed and operational within 2-4 weeks."
  }
];

const SOLUTIONS = [
  {
    title: "Touchscreen Interface",
    description:
      "Visitors can interact with maps through an easy-to-use, touch-friendly interface, making it simple to search for locations and explore the indoor space.",
    iconUrl: "/images/icons/route.png",
    color: "#34A853",
  },
  {
    title: "Instant Access to Maps",
    description:
      "No need for personal devices or downloads—the kiosk provides immediate access to maps, allowing visitors to find their way without extra steps.",
    iconUrl: "/images/icons/wifi.png",
    color: "#34A853",
  },
  {
    title: "Up-to-Date Information",
    description:
      "The kiosk displays current maps of the indoor space, making sure that users always have the latest layout information.",
    iconUrl: "/images/icons/human-resource.png",
    color: "#34A853",
  },
];

const accordionData = [
  {
    title: "Easily Integrate Maps",
    content:
      "Integrate map of your retail space into existing apps to effortlessly guide shoppers to their favorite stores, restaurants, and services via the shortest route.",
  },
  {
    title: "Multi-Language Support",
    content:
      "Make your mall map accessible to all visitors by offering it in multiple languages. beComap allows you to provide directions and information in the shopper’s preferred language, making the mall experience more personalized and user-friendly. This feature is particularly valuable in diverse or tourist-heavy locations.",
  },
  {
    title: "Give Updates on Store Offers",
    content:
      "Keep shoppers in the loop with instant updates on store deals, upcoming events, and essential services, improving their overall shopping experience.",
  },
  {
    title: "Less Searching, More Shopping",
    content:
      "Navigate in a flash with our system—find the fastest route and shortest path from parking to stores, or multi-stop routes to plan your shopping with less walking and more items on your cart.",
  },
];

const ServiceData = [
  {
    title: "1. Choose the Right Location",
    content:
      "The first step is selecting the most effective spots for placing the kiosks. We work with you to identify high-traffic areas or key points within your indoor space where visitors are most likely to seek help. These could be entrances, near elevators, or central locations where people naturally gather. By positioning kiosks in these strategic spots, we make sure they are easily accessible and visible to everyone, helping visitors find them quickly when they need directions or information.",
    image: "/images/choose-the-right-location.png",
  },
  {
    title: "2. Create Digital Maps",
    content:
      "Next, we create detailed digital maps of your space. These maps are carefully developed to show important areas such as entrances, rooms, service zones, and facilities like restrooms or customer service desks. We also make sure these maps are regularly updated, so any changes to your layout, such as new rooms or temporary closures, are reflected accurately. Visitors will always have the most current information, allowing them to navigate your space with confidence.",
    image: "/images/create-digital-maps.png",
  },
  {
    title: "3. Develop the Kiosk Interface",
    content:
      "Once the maps are ready, we focus on designing a simple and user-friendly interface for the kiosks. The goal is to make it easy for visitors to access maps and follow step-by-step directions directly from the screen. The interface is clear with names and zones, allowing visitors to quickly search for specific locations and view their routes. Whether they’re looking for a store, a room, or a service area, the kiosk guides them with clear instructions, helping them move around without confusion.",
    image: "/images/develop-the-kiosk-interface.png",
  },
];

const WHY_DATA = [
  {
    title: "Access the Map on the Kiosk",
    description:
      "Visitors can interact directly with the kiosk screen, providing a touch-friendly interface that displays the full indoor map.",
    iconUrl: "/images/icons/locations.png",
    activeIconUrl: "/images/icons/locations-active.png",
    color: "#34A853",
  },
  {
    title: "Search for a Location",
    description:
      "Users can search for specific locations like stores, rooms, or service areas right from the kiosk.",
    iconUrl: "/images/icons/search-map.png",
    activeIconUrl: "/images/icons/search-map-active.png",
    color: "#34A853",
  },
  {
    title: "Explore the Map",
    description:
      "Users can pan, zoom, and explore the entire indoor space to understand the layout and available services.",
    iconUrl: "/images/icons/type.png",
    activeIconUrl: "/images/icons/type-active.png",
    color: "#34A853",
  },
  {
    title: "Navigate to Destination",
    description:
      "The kiosk provides step-by-step directions on-screen, guiding visitors to their desired location within the indoor space.",
    iconUrl: "/images/icons/navigate-map.png",
    activeIconUrl: "/images/icons/navigate-map-active.png",
    color: "#34A853",
  },
];

const Kiosk = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <SolutionBanner
        source="kiosk"
        bgImage={HeroImg}
        title="Transform User Navigation with Kiosk Wayfinder"
        description="beComap’s Kiosk offers a self-service solution for indoor <br /> navigation, allowing visitors to easily find their way in malls, <br /> airports, hospitals, or event venues, without requiring a personal <br /> device."
      />
      <IndustryClients />
      <SolutionWhyUs
        title={"What Is Kiosk?"}
        description={
          "beComap’s Kiosk helps visitors to access interactive indoor maps and get directions without needing a smartphone or any other device. Located in high-traffic areas, the kiosk provides an easy-to-use interface that helps users search for destinations, view maps, and receive clear directions right on the screen"
        }
        bgImage={SolutionWhyUsBg}
        subImage={WhySubImage}
        whyData={WHY_DATA}
      />
      <IndustrySolutions
        image={SecImg}
        videoSrc={"/videos/solution.mp4"}

        solutions={SOLUTIONS}
        title={"What Makes the Kiosk Effective?"}
        description={{
          part1:
            "The kiosk offers a simple way for visitors to find their way without needing a smartphone. Placed in key areas of your venue, it lets users search for locations, view maps, get location details and get directions without having to download an app or use their personal device. This makes it especially useful for people who prefer not to rely on their phones or may not have access to one.",
          part2:
            "With its easy-to-use interface, the kiosk helps visitors quickly find important areas like entrances, restrooms, stores, or departments. This reduces the chance of them getting lost in a large space, saving time and frustration. The kiosk is particularly helpful in busy places like malls, airports, and hospitals, where quick access to information is needed.",
          part3:
            "For venue owners, the kiosk reduces the need for staff to assist with directions and helps visitors navigate on their own. It's an efficient solution for improving the visitor experience by providing clear, self-service options for finding their way through large or complex spaces.",
        }}
      />

  

      <SolutionsServices
        title="How We Set Up Your Kiosk for Indoor Navigation"
        description="Setting up your kiosk for indoor navigation includes several key steps to ensure a smooth experience for visitors. From choosing the best location for the kiosk to developing the map interface, we handle everything to make it easy for users to navigate your indoor space."
        image={"/images/kiosk-dummy.png"}
        serviceData={ServiceData}
      />
      <section
        className="section primary-bg"
        style={{
          backgroundImage: `url("/images/solution-bg.png")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          minHeight: "680px",
        }}
      >
        <Container>
          <Row className="align-items-center">
            <Col lg={7}>
              <Image
                src={"/images/solution-sec.png"}
                alt="indoor mapping software"
                width={550}
                height={450}
                style={{ width: "100%", height: "auto" }}
              />
            </Col>
            <Col lg={5}>
              <div className="section-head">
                <span
                  className="mb-4"
                  style={{
                    fontWeight: 600,
                    fontSize: "18px",
                  }}
                >
                  Case Study
                </span>
                <h2 className="main-title mb-3">Indoor mapping software</h2>
                <p>
                  Develop an ultra-modern travel environment that ree
                  experiences. Set new standards in travel convenience and
                  satisfaction through innovation, sustainability, and
                  operational efficiency.
                </p>
              </div>
              <button className="btn btn-dark mt-4">Learn More</button>
            </Col>
          </Row>
        </Container>
      </section>
      {/* <IndustryEBook
        image={"/images/mall-ebook-bg.png"}
        title={"Retail In Action"}
        description={`<p>According to ACI, global passenger traffic will reach 9.7 billion this year.</p> <p>Learn how to deliver a smooth travel experience with indoor maps in our helpful retail guide.</p>`}
      /> */}
      <Blogs />
      <TalkToOurTeam source={"kiosk"} />
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={mallNavigationFAQ}
      />
    </div>
  );
};

export default Kiosk;
