.accordion-section {
  background-color: #279845E0;
  background-repeat: no-repeat;
  background-size: cover;

  @media (max-width: 991px) {
    padding: 60px 0;
    .main-title {
      font-size: 36px;
      line-height: 40px;
    }
  }
  @media (max-width: 767px) {
    .company-value-head {
      margin-bottom: 30px;
    }
  }

  .company-value-head {
    .main-title {
      color: #ffffff;
    }
    p {
      line-height: 26px;
      color: #ffffff;
      font-size: 15px;
    }
  }

  .video-wrapper {
    box-shadow: 0px 20px 50px 0px #00000014;
    border-radius: 30px;
    overflow: hidden;
    object-fit: contain;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 30px;
    position: relative;
    @media (max-width: 1199px) {
      margin: 0 40px 60px;
    }
    @media (max-width: 767px) {
      margin: 0 0 60px;
    }
    video {
      width: auto;
      height: 100%;
      max-height: 402px;
      @media (max-width: 1199px) {
        width: 100%;
        height: auto;
        max-height: unset;
      }
    }

    // &::before {
    //   content: "";
    //   position: absolute;
    //   width: 100%;
    //   height: 100%;
    //   background: #FFFFFF4D;

    // }
  }
  .accordion {
    margin-left: 30px;
    color: #ffffff;
    @media (max-width: 1399px) {
      margin-left: 0;
    }
    .accordion-item {
      border: none;
      color: #ffffff;
      margin-bottom: 15px;
      background-color: transparent;

      .accordion-header {
        color: #ffffff;
        .accordion-button {
          background-color: transparent;
          color: #ffffff;
          font-family: $font-medium;
          font-size: 18px;
          font-weight: 500;
          line-height: 30px;
          text-align: left;
          padding-top: 15px;
          padding-left: 0;
          &::after {
            color: #ffffff;
            background-image: url("/images/icons/arrow-down.png");
          }
          &:not(.collapsed) {
            color: #ffffff;
            background-color: transparent;
            box-shadow: none;
          }
        }
        border-bottom: 1px solid #CECECE;
      }
    }
    .accordion-body {
      border-top: 1px solid #CECECE;
      padding: 28px 20px;
      background: #006A1D;
      font-family: $font;
      font-size: 15px;
      line-height: 26px;
      text-align: left;
    }
  }
}
