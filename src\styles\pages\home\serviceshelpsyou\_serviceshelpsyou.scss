.srv-helpslide-box-mid {
  flex: 1;
  @media (max-width: 1199px) {
    align-items: center;
    justify-content: center;
    width: 100vw;
  }
  @media (max-width: 768px) {
    flex-direction: column;
    width: 100vw;
    overflow: hidden;
  }
  .srv-helpslide-imgbox {
    // width: 25%;
    position: relative;
    height: 500px;
    padding-top: 10px;
    @media (max-width: 767px) {
      height: auto;
      width: 500px !important;
    }
    @media (max-width: 370px) {
      justify-content: center !important;
    }
    &:first-child::after {
      display: none;
    }
    &::after {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 4px;
      border-radius: 100px;
      background-color: #000;
      content: "";
      @media (max-width: 768px) {
        width: auto;
        height: 4px;
        right: 0;
        left: 0;
        bottom: auto;
      }
    }
    h4 {
      font-size: 20px;
      font-family: $font-bold;
      line-height: 24px;
      padding: 16px 24px;
    }
  }
}

.srv-helpslide-box-right {
  width: 300px;
}
.srv-helpslide-box-left {
  width: 177px;
  margin-right: 120px;
  @media (max-width: 1399px) {
    margin-right: 5%;
  }
  @media (max-width: 1199px) {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom: 50px;
    margin-right: 0;
    flex-wrap: wrap;
    gap: 20px;
    .srv-count-box {
      margin: 0;
    }
  }
}
.services-helpsyou-section {
  .swiper {
    flex: 1;
  }
  .section-head {
    max-width: 630px;
    text-align: center;
    margin: 0 auto;
  }
  .swiper-button-prev {
    width: 49px;
    height: 49px;
    background-image: url("/images/icons/right-arrow.png");
    right: 210px;
    position: absolute;
    top: 250px;
    left: inherit;
    background-size: 49px;
    background-repeat: no-repeat;
    transform: rotate(180deg);
    z-index: 4;
    &::after {
      display: none;
    }

    @media (max-width: 1199px) {
      right: auto;
      left: 50%;
      transform: translateX(-50%) rotate(180deg);
      top: 100%;
      top: 110%;
    }
  }
  .swiper-button-next {
    width: 49px;
    height: 49px;
    background-image: url("/images/icons/right-arrow.png");
    right: 152px;
    position: absolute;
    top: 250px;
    left: inherit;
    background-size: 49px;
    background-repeat: no-repeat;
    z-index: 4;
    &::after {
      display: none;
    }
    @media (max-width: 1199px) {
      right: auto;
      left: 50%;
      transform: translateX(-50%);
      margin-left: 55px;
      top: 110%;
    }
  }
}
.srv-count-box {
  margin-top: 84px;

  &:first-child {
    margin-top: 0;
    padding-top: 5px;
  }
  h2 {
    font-size: 50px;
    font-family: $font-bold;
    line-height: 50px;
    margin-bottom: 8px;
  }
  .srv-count-title {
    width: 177px;
    height: 50px;
    // background: $secondary-color;
    border-radius: 10px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    font-size: 20px;
    font-family: $font-medium;
  }
}
.srv-slide-content {
  padding: 27px 0 27px 48px;
  position: relative;
  font-family: $font-medium;
  @media (max-width: 1199px) {
    display: none;
  }
  &::after {
    position: absolute;
    left: 0;
    top: 0px;
    bottom: 0px;
    background-color: $secondary-color;
    content: "";
    width: 95px;
    z-index: -1;
  }
}
.srv-helpslide-box-right {
  padding-left: 40px;
}

.srv-helpslide-imgbox1 {
  width: 223px;
  .helpyou-overlay-wrap {
    border-radius: 50%;
    width: 263px !important;
    height: 263px !important;
    overflow: hidden;
  }

  @media (max-width: 1199px) {
    width: 230px !important;
    .helpyou-overlay-wrap {
      width: 320px !important;
      height: 320px !important;
    }
  }
  @media (max-width: 768px) {
    width: auto !important;
    height: 230px !important;
  }
}

.srv-helpslide-imgbox2 {
  width: 201px;

  .helpyou-overlay-wrap {
    border-radius: 50%;
    width: 291px !important;
    height: 291px !important;
    overflow: hidden;
    margin-left: -40px;
    @media (max-width: 768px) {
      margin-left: 0;
      margin-top: -50px;
    }
    // @media (max-width: 768px) {
    //     mar
    // }
  }
}

.srv-helpslide-imgbox3 {
  width: 200px;
  .helpyou-overlay-wrap {
    border-radius: 50%;
    width: 345px !important;
    height: 345px !important;
    overflow: hidden;
    margin-left: -60px;
    @media (max-width: 768px) {
      margin-left: 0;
      margin-top: -60px;
    }
  }
}

.srv-helpslide-imgbox4 {
  width: 192px;
  .helpyou-overlay-wrap {
    border-radius: 50%;
    width: 374px !important;
    height: 374px !important;
    overflow: hidden;
    margin-left: -160px;

    @media (max-width: 768px) {
      margin-left: 0;
      margin-top: -160px;
    }
    @media (max-width: 370px) {
      margin-top: -50px;
    }
  }
  .helpyou-overlay-item span {
    margin-right: -100px;
  }
}

.helpyou-overlay-wrap {
  position: relative;
}
.helpyou-overlay-item {
  display: none;
  background-color: #000000;
  cursor: pointer;
  span {
    button.btn {
      min-width: unset;
      height: auto;
    }
  }
}
.srv-helpslide-imgbox {
  overflow: hidden;
}

.srv-helpslide-imgbox:hover {
  .helpyou-overlay-item {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    align-items: center;
    justify-content: center;
    z-index: 4;
  }
}
.services-helpsyou-section .container {
  max-width: 1500px;
}
.dropdown-toggle::after {
  display: none !important;
}

@media (max-width: 1600px) {
  .srv-helpslide-imgbox1 {
    width: 183px;
    .helpyou-overlay-wrap {
      width: 223px !important;
      height: 223px !important;
    }
  }

  .srv-helpslide-imgbox2 {
    width: 161px;

    .helpyou-overlay-wrap {
      width: 251px !important;
      height: 251px !important;
    }
  }

  .srv-helpslide-imgbox3 {
    width: 160px;
    .helpyou-overlay-wrap {
      width: 305px !important;
      height: 305px !important;
    }
  }

  .srv-helpslide-imgbox4 {
    width: 152px;
    .helpyou-overlay-wrap {
      width: 334px !important;
      height: 334px !important;
    }
  }
  .services-helpsyou-section .container {
    max-width: 1320px;
  }
}
// @media (max-width: 1300px) {
//     .srv-helpslide-imgbox1 {
//         width: 153px;
//         .helpyou-overlay-wrap {
//             width: 193px !important;
//             height: 193px !important;
//         }
//     }

//     .srv-helpslide-imgbox2 {
//         width: 131px;

//         .helpyou-overlay-wrap {
//             width: 221px !important;
//             height: 221px !important;
//         }
//     }

//     .srv-helpslide-imgbox3 {
//         width: 130px;
//         .helpyou-overlay-wrap {
//             width: 235px !important;
//             height: 235px !important;
//         }
//     }

//     .srv-helpslide-imgbox4 {
//         width: 122px;
//         .helpyou-overlay-wrap {
//             width: 264px !important;
//             height: 264px !important;
//         }
//     }
//     .services-helpsyou-section .container {
//         max-width: 1000px;
//         .swiper-button-prev {
//             top: inherit;
//             bottom: 30%;
//             right: 142px;
//         }
//         .swiper-button-next {
//             right: 82px;
//             top: inherit;
//             bottom: 30%;
//         }
//     }
//     // .srv-helpslide-box-left {
//     //     width: 150px;
//     //     margin-right: 40px;
//     // }
//     .srv-count-box {
//         margin-top: 84px;
//         h2 {
//             font-size: 40px;
//             line-height: 40px;
//         }
//         .srv-count-title {
//             width: 150px;
//             font-size: 20px;
//         }
//     }
//     .srv-helpslide-box-mid {
//         .srv-helpslide-imgbox {
//             height: 440px;

//             h4 {
//                 max-width: 100%;
//                 font-size: 20px;
//                 line-height: 22px;
//                 padding: 8px 12px;
//                 margin: 0;
//             }
//         }
//     }
//     .srv-slide-content {
//         padding-left: 20px;
//         font-size: 13px;
//     }
// }
.srv-helpslide-box {
  position: relative;
  @media (max-width: 1199px) {
    flex-direction: column;
  }
}
@media (max-width: 767px) {
  .srv-helpslide-box {
    flex-direction: column;
  }
  .srv-helpslide-box-left {
    width: 100%;
    display: flex;
    margin-bottom: 40px;
  }
  .srv-helpslide-box-right {
    width: 100%;
    margin-bottom: 50px;
    margin-top: 50px;
    padding-left: 0;
  }
  .srv-count-box {
    margin-top: 0;
    text-align: center;
    h2 {
      font-size: 30px;
      line-height: 30px;
    }
    .srv-count-title {
      font-size: 16px;
      height: 40px;
    }
  }
  .srv-count-box:first-child {
    padding-top: 0;
  }
  .srv-count-title {
    justify-content: center;
  }

  .srv-helpslide-box-mid {
    .srv-helpslide-imgbox {
      height: 300px;
      padding-top: 0px;
      @media (max-width: 767px) {
        height: 200px;
      }
      h4 {
        max-width: 162px;
        font-size: 18px;
        line-height: 24px;
        padding: 0 15px;
        display: flex;
        align-items: center;
        margin: 0;
        @media (max-width: 370px) {
          position: absolute;
          z-index: 3;
          top: 50%;
          left: 50%;
          transform: translateY(-50%) translateX(-50%);
          text-align: center !important;
          background: #fafafccc;
          -webkit-backdrop-filter: saturate(180%) blur(20px);
          backdrop-filter: saturate(180%) blur(20px);
          width: 100%;
          max-width: 240px;
          justify-content: center;
          padding: 8px 15px;
          border-radius: 8px;
        }
      }
    }
  }
  .services-helpsyou-section {
    .swiper-button-prev {
      width: 36px;
      height: 36px;
      background-size: 36px;
      bottom: 0 !important;
      right: calc(50% + 20px) !important;
      left: unset;
      top: unset;
      transform:  rotate(180deg);
     
    }
    .swiper-button-next {
      width: 36px;
      height: 36px;
      background-size: 36px;
      top: unset;
      bottom: 0 !important;
      right: calc(50% - 20px) !important;
      margin: 0;
      transform: unset;
      left: unset;
    }
  }
}

.srv-helpslide-imgbox1 {
  @media (max-width: 1199px) {
    width: 230px !important;
    .helpyou-overlay-wrap {
      width: 280px !important;
      height: 280px !important;
    }
  }
}

.srv-helpslide-imgbox2 {
  @media (max-width: 1199px) {
    width: 200px !important;
    .helpyou-overlay-wrap {
      width: 280px !important;
      height: 280px !important;
    }
  }
}

.srv-helpslide-imgbox3 {
  @media (max-width: 1199px) {
    width: 193px !important;
    .helpyou-overlay-wrap {
      width: 280px !important;
      height: 280px !important;
    }
  }
}

.srv-helpslide-imgbox4 {
  @media (max-width: 1199px) {
    width: 193px !important;
    .helpyou-overlay-wrap {
      width: 370px !important;
      height: 370px !important;
    }
  }
  @media (max-width: 370px) {
    .helpyou-overlay-wrap {
      width: 280px !important;
      height: 280px !important;
    }
  }
}

// @media (max-width: 768px) {
//     .helpyou-overlay-wrap{
//         width: auto!important;
//         height: auto!important;
//     }
// }

.content-slider {
  display: flex;
  overflow: hidden;
  transition: transform 0.3s ease;
  transform: translateX(0);
}

.slide {
  transform: translateX(-100%);
}

.content-box {
  min-width: 100px;
  padding: 20px;
  margin: 0 10px;
  border: 1px solid #ccc;
  text-align: center;
}

.srv-help-slider {
  width: 100%;
}
