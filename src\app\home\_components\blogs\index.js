"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import Container from "react-bootstrap/Container";
import BlogContent from "./_components/blogcontent";
import { Autoplay } from "swiper/modules";
import { getCarouselBlog } from "@/lib/actions";
import { useEffect, useState } from "react";

const Blogs = () => {
  const [blogData, setBlogData] = useState([]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    const dataBog = await getCarouselBlog(1, 20);
    setBlogData(dataBog.results);
  };

  return (
    <div className="section blog-section-wrap">
      {blogData ? <>
      <Container>
        <div className="section-head text-center">
          <h2 className="main-title mb-5">Read our Latest blogs</h2>
        </div>
      </Container>
      <Swiper
        slidesPerView={"auto"}
        className="blogslider"
        centeredSlides={true}
        loop={true}
        autoplay={{
          delay: 2500,
          disableOnInteraction: false,
        }}
        modules={[Autoplay]}
        speed={2000}
      >
        {blogData?.map((blog) => (
          <SwiperSlide key={blog.url || blog.id}>
            <BlogContent
              writer={blog.author.name}
              title={blog?.title}
              content={blog.description}
              imgUrl={blog.image}
              slug={blog.url}
            />
          </SwiperSlide>
        ))}
      </Swiper>
      </> : null}
    </div>
  );
};

export default Blogs;
