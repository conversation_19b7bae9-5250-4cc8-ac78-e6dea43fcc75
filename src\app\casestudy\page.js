import CaseStudyItem from './_components/casestudyitem';
import CaseStudyBanner from './_components/casestudybanner';
import { TalkToOurTeam } from '@/components/sections';


const caseStudyData = [
    { id: 'oasis-living-1', image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { id: 'another-project-1', image: '/images/casestudy-img-1.png', title: 'Another Project', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { id: 'oasis-living-2', image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { id: 'another-project-2', image: '/images/casestudy-img-1.png', title: 'Another Project', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { id: 'oasis-living-3', image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { id: 'another-project-3', image: '/images/casestudy-img-1.png', title: 'Another Project', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { id: 'oasis-living-4', image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { id: 'another-project-4', image: '/images/casestudy-img-1.png', title: 'Another Project', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { id: 'oasis-living-5', image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    // Add more sections as needed
];


const CaseStudy = () => {
    return (
        <div className='casestudy-wrap'>
            <CaseStudyBanner />
            {caseStudyData.map((section, index) => (
                <CaseStudyItem
                    key={section.id}
                    index={index}
                    image={section.image}
                    content={section.content}
                    title={section.title}
                    description={section.description}
                />
            ))}
            <TalkToOurTeam source={"casestudy"} />
        </div>
    );
};

export default CaseStudy;
