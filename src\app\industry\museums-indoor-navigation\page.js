import IndustryBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryEBook from "../_components/IndustryEBook";
import IndustryHelp from "../_components/IndustryHelp";
import HeroImg from "@/assets/images/industry/museums-hero.png";
import SecImg3 from "@/assets/images/industry/event-help-illu.png";
import { TalkToOurTeam } from "@/components/sections";
import BgImg from "@/assets/images/industry/museums-accordion-bg.png";
import FaqSection from "../_components/FaqSection";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: "Improve Museum Experiences with Indoor Navigation| Becomap",
  description:
    "Enhance museum experiences with Becomap’s interactive indoor navigation system. Guide visitors through exhibits and elevate engagement effortlessly.",
  images: ["/becomap.png"],
  openGraph: {
    title: "Improve Museum Experiences with Indoor Navigation| Becomap",
    description:
      "Enhance museum experiences with Becomap’s interactive indoor navigation system. Guide visitors through exhibits and elevate engagement effortlessly.",
    images: ["/becomap.png"],
  },
};

const eventNavigationFAQ = [
  {
    question: "Do visitors need to download an app to use Becomap?",
    answer: "No, there’s no need to download an app! Visitors simply scan a QR code to instantly access Becomap’s navigation system through their mobile browser, making it quick and easy to start navigating."
  },
  {
    question: "Does the navigation support multiple languages for international visitors?",
    answer: "Absolutely! The system offers multi-language support, enabling visitors to receive directions in their preferred language, making the museum more inclusive and accessible to a global audience."
  },
  {
    question: "What privacy protections are in place for data collection?",
    answer: "Visitor privacy is a top priority. The system collects anonymous data to improve museum management and visitor flow, without tracking individual identities, enabling museums to gain actionable insights while respecting visitor privacy."
  }
];

const SOLUTIONS = [
  {
    title: "Clear Directions",
    description:
      "beComap’s Indoor navigation offers clear, easy-to-follow directions for patients and visitors to find their way through the hospital. From rooms to pharmacies, visitors can navigate smoothly, reducing confusion and saving time.",
    iconUrl: "/images/icons/patient.png",
    color: "#00BEDA",
  },
  {
    title: "Make Essential Services Easy to Find",
    description:
      "In a busy hospital, services like emergency rooms, labs, and pharmacies are crucial. beComap lets you highlight these key areas so patients can reach them without delay. No more guessing, just clear directions to the care they need.",
    iconUrl: "/images/icons/coins.png",
    color: "#ED55B9",
  },
  {
    title: "Use Data to Improve Efficiency",
    description:
      "Understanding how patients and visitors move through your hospital can help you optimize operations. beComap’s data insights allow you to monitor foot traffic, improve your layout, and keep things running smoothly—all without intruding on user privacy.",
    iconUrl: "/images/icons/management.png",
    color: "#ED790F",
  },
];

const accordionData = [
  {
    title: "Multi-Language Support",
    content:
      "Offer written directions in multiple languages, making your museum more accessible for international guests. beComap ensures an inclusive, user-friendly experience.",
  },
  {
    title: "Instant Alerts",
    content:
      "Keep visitors updated on exhibit changes or closures with notifications. beComap enables quick communication, ensuring everyone stays informed.",
  },
  {
    title: "Less Searching, More Discovering",
    content:
      "beComap helps guests quickly find routes to exhibits and workshops, allowing them to spend more time enjoying the museum instead of searching for their next stop.",
  },
  {
    title: "Stress-Free Navigation",
    content:
      "Large museums can be confusing, but beComap provides step-by-step directions, helping visitors navigate easily and confidently, whether they're new or returning.",
  },
  {
    title: "No App Downloads Required",
    content:
      "Visitors can scan a QR code for instant access to beComap's navigation through their browser, without the need for app downloads, making the process simple and convenient.",
  },
  {
    title: "Data-Driven Museum Management",
    content:
      "beComap helps museum managers make data-backed decisions by tracking visitor movement through the museum. This data enables optimization of exhibit placement, foot traffic management, and improvement of the overall visitor experience, all while maintaining privacy. Our insights refine operations & enhance the guest experience based on real visitor behavior.",
  },
];

const ServiceData = [
  {
    title: "QR Code Scanning. No Downloads Needed",
    content:
      "beComap’s web-based solution means there are no app downloads required. Patients and visitors simply scan a QR code at the entrance to access step-by-step directions, making navigation effortless without additional hassle.",
    icon: "/images/icons/rfid.png",
    image: "/images/qr-code-scanning-health.png",
  },
  {
    title: "Parking Made Simple",
    content:
      "Finding a parking spot shouldn’t be another headache for visitors. beComap organizes hospital parking into zones with QR codes. Visitors scan the code when they park and are guided back to their vehicle when it’s time to leave, saving them from wandering around the lot.",
    icon: "/images/icons/parked-car.png",
    image: "/images/parking-made-simple.png",
  },
  {
    title: "Optimize Hospital Operations",
    content:
      "Managing a hospital involves a lot of moving parts. beComap helps you track equipment, manage space, and assign tasks efficiently, all from one simple dashboard. You’ll have everything you need to make your hospital run like a well-organized system, keeping both patients and staff happy.",
    icon: "/images/icons/pharmacy.png",
    image: "/images/optimize-hospital-operations.png",
  },
  {
    title: "Tailored Maps for Your Hospital’s Branding",
    content:
      "Customize your hospital’s map to reflect its branding with beComap’s flexible design options. From logos to color schemes, beComap ensures your hospital map is visually consistent with your identity while enhancing the patient experience.",
    icon: "/images/icons/map01.png",
    image: "/images/tailored-maps.png",
  },
];


const Museums = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <IndustryBanner
        routeType="museums"
        bgImage={HeroImg}
        title={`Transform Museum Visits with Interactive Indoor Navigation System`}
        description="Transform your museum into a well-organized space where visitors can easily explore exhibits and engage deeply with your collections."
      />
      <IndustryClients />
      <section
        className="section bg-bc-light "
        style={{ position: "relative" }}
      >
        <div className="company-value-head mb-2">
          <h2 className="main-title">Clear Paths, Engaged Visitors</h2>
          <p>
            {`Museums are spaces for discovery and appreciation, but visitors often struggle to find their way, missing key exhibits and experiences. beComap’s Indoor Navigation Solution transforms this journey, offering step-by-step directions that guide guests effortlessly from entry points to exhibits, restrooms, and more. Visitors spend less time wondering where to go and more time immersed in your collections.`}
          </p>
        </div>
        {/* <div
          ref={animRef}
          // className="bg-anim-ort"
        >
          <svg
            width="auto"
            height="100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2 5H739C747.284 5 754 11.7157 754 20V80C754 88.2843 760.716 95 769 95H1147C1155.28 95 1162 88.2843 1162 80V20C1162 11.7157 1168.72 5 1177 5H1920"
              stroke="white"
              strokeWidth="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div> */}
        <div className="event-sec">
          <Container fluid={"xl"}>
            <Row>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#00BEDA" }}
                    >
                      <Image
                        src={"/images/icons/patient.png"}
                        alt={"Unmatched Visitor Experience"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">Unmatched Visitor Experience</h4>
                  </div>
                  <p className="">
                    beComap ensures that visitors can effortlessly discover key
                    exhibits, hidden gems, and interactive installations,
                    enhancing their experience and encouraging deeper
                    exploration.
                  </p>
                </div>
              </Col>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5 mt-lg-5 mt-4">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#ED55B9" }}
                    >
                      <Image
                        src={"/images/icons/signal-stream.png"}
                        alt={"Stay Ahead of the Action"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">Stay Ahead of the Action</h4>
                  </div>
                  {/* eslint-disable react/no-unescaped-entities */}
                  <p className="mb-0">
                    Visitors are instantly notified about special exhibits, live
                    demonstrations, or events happening throughout the day,
                    keeping them engaged and informed during their visit.
                  </p>
                </div>
              </Col>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5 mt-lg-0 mt-4">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#ED790F" }}
                    >
                      <Image
                        src={"/images/icons/location-1.png"}
                        alt={"Navigate with Confidence"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">Navigate with Confidence</h4>
                  </div>
                  <p className="mb-0">
                    beComap offers detailed information on accessible routes,
                    elevators, and visitor services, allowing all guests to
                    comfortably and confidently explore the museum's offerings.
                  </p>
                </div>
              </Col>
              {/* eslint-enable react/no-unescaped-entities */}
            </Row>
          </Container>
        </div>
        {/* <svg
          width="auto"
          height="74"
          // viewBox="0 0 1920 74"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1941 5H1240.14C1231.85 5 1225.14 11.7157 1225.14 20V54C1225.14 62.2843 1218.42 69 1210.14 69H-21"
            stroke="white"
            strokeOpacity="0.5"
            strokeWidth="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg> */}
      </section>
      <IndustryAccordion
        title={"Improve Visitor Experience and Optimize Museum Flow"}
        description={
          "By providing clearer navigation and easier exhibit discovery, beComap enhances the overall museum visit, allowing guests to enjoy their time and explore more effectively. This encourages return visits and greater engagement with your museum. Additionally, the data collected through beComap helps you refine exhibit placement and manage visitor movement, creating a better flow through the museum and helping guests interact more naturally with your exhibits."
        }
        bgImage={BgImg}
        accordionData={accordionData}
        videoSrc={"/videos/mall.mp4"}
      />
      <IndustryHelp
        title="Why Leading Museums Trust beComap"
        description="beComap changes how you think about museum navigation and visitor engagement. Our platform makes navigation simple, encourages return visits, and optimizes museum operations for a better overall guest journey."
        image={SecImg3}
        points={[
          "Trusted by world-class museums for indoor navigation",
          "8 years of experience in location technology",
          "Optimized for use on mobile devices, web browsers, and kiosks",
          "Easily integrates with existing museum apps",
          "Web-based navigation without downloads",
          "Multi-language support for global audiences"
        ]}
      />

      <IndustryEBook
        image={"/images/event-ebook-bg.png"}
        title={"Logistics In Action"}
        description={`<p>Unlock indoor navigation potential with our eBook. </p> <p>Explore technologies, strategies, and best practices to enhance wayfinding, visitor experience, and operations.</p>`}
      />

      <TalkToOurTeam source={"museums"}/>
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={eventNavigationFAQ}
      />
    </div>
  );
};

export default Museums;
