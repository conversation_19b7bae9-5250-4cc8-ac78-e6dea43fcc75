"use client";
import { useState, useEffect } from "react";
import use<PERSON><PERSON> from "swr";
import axios from "axios";
import { Container, Row, Col } from "react-bootstrap";
import BlogPost from "../blogpost";
import BlogSearch from "../search";
import BlogSubscribe from "../subscribe";
import BlogEbook from "../ebook";

import Button from "react-bootstrap/Button";
import BlogLoader from "../blogLoader";

// Fetcher function for SWR with correct URL structure
const fetcher = (url) =>
  axios
    .get(url, {
      headers: {
        "X-Blog-Identifier": "beco",
      },
    })
    .then((res) => res.data);

const BlogList = () => {
  const [pageSize, setPageSize] = useState(13); // Default page size
  const [page, setPage] = useState(1); // Current page number
  const [allPosts, setAllPosts] = useState([]); // Initialize with an empty array
  const [loadingMore, setLoadingMore] = useState(false); // Track loading state for "Read More"
  const [noMorePosts, setNoMorePosts] = useState(false); // Track if there are no more posts
  const [loading, setLoading] = useState(true);

  // Construct URL with correct query params
  const blogUrl = `https://blog-api.becomap.com/posts/?page=${page}&page_size=${pageSize}`;

  // Use SWR to fetch data with dynamic page size and page number
  const { data } = useSWR(blogUrl, fetcher, {
    onSuccess: () => setLoading(false), // Set loading to false on data load
    onError: () => setLoading(false), // Handle loading state on error as well
  });

  // Update allPosts when new data arrives
  useEffect(() => {
    if (data && data.results) {
      if (data.results.length < pageSize) {
        setNoMorePosts(true); // No more posts if less than pageSize
      }
      setAllPosts((prevPosts) => [...prevPosts, ...data.results]);
    }
  }, [data, pageSize]); // Runs when data changes

  // Handle loading more posts
  const loadMorePosts = () => {
    if (loadingMore || noMorePosts) return; // Prevent multiple clicks and loading if no more posts
    setLoadingMore(true);
    setPage((prev) => prev + 1); // Increment page number
    setPageSize((prev) => prev + 11); // Increase page size by 10
  };

  useEffect(() => {
    setLoadingMore(false); // Reset loading state after data is fetched
  }, [data]); // When data changes, reset loading state

  // if (error) return <Loading />;
  // if (!data && allPosts.length === 0) return <Loading />;

  return (
    <>
      {/* {allPosts.length > 0 && ( */}
      <Container className="blog-container" fluid="xl">
        <div className="section-head text-center">
          <h2 className="main-title mb-4">Read our Latest blogs</h2>
        </div>
        <div className="blog-top-row d-flex">
          <div className="blog-top-post-left">
            {loading ? (
              <BlogLoader imageHeight={392} />
            ) : (
              <BlogPost item={allPosts[0]} imageHeight={392} />
            )}
          </div>
          <div className="blog-top-post-right">
            <div className="blog-sub-boxes">
              <BlogSearch />
            </div>
            <div className="blog-sub-boxes">
              <BlogSubscribe />
            </div>
          </div>
        </div>

        <Row className="blog-post-smallbox">
          <Col lg={6} md={6}>
            {loading ? (
              <BlogLoader imageHeight={371} />
            ) : (
              <BlogPost item={allPosts[1]} imageHeight={371} />
            )}
          </Col>
          <Col lg={6} md={6}>
            {loading ? (
              <BlogLoader imageHeight={371} />
            ) : (
              <BlogPost item={allPosts[2]} imageHeight={371} />
            )}
          </Col>
          {loading
            ? [1, 2, 3].map((key) => (
                <Col lg={4} md={6} key={key}>
                  <BlogLoader imageHeight={246} />
                </Col>
              ))
            : allPosts.slice(3, 6).map((item) => (
                <Col lg={4} md={6} key={item.id || item.url}>
                  <BlogPost item={item} imageHeight={246} />
                </Col>
              ))}

          <Col lg={12}>
            <Row>
              <Col lg={8}>
                <Row>
                  {loading
                    ? [1, 2, 3, 4].map((key) => (
                        <Col lg={4} md={6} key={key}>
                          <BlogLoader imageHeight={246} />
                        </Col>
                      ))
                    : allPosts.slice(6, 10).map((item) => (
                        <Col lg={6} md={6} key={item.id || item.url}>
                          <BlogPost item={item} imageHeight={246} />
                        </Col>
                      ))}
                </Row>
              </Col>
              <Col lg={4}>
                <BlogEbook />
              </Col>
            </Row>
            <Row>
              {loading
                ? [1, 2, 3].map((key) => (
                    <Col lg={4} md={6} key={key}>
                      <BlogLoader imageHeight={246} />
                    </Col>
                  ))
                : allPosts.slice(10).map((item) => (
                    <Col lg={4} md={6} key={item.id || item.url}>
                      <BlogPost item={item} imageHeight={246} />
                    </Col>
                  ))}
            </Row>
          </Col>
        </Row>

        {/* "Read More" Button */}
        <div className="d-flex justify-content-center mt-4 mb-5">
          {noMorePosts ? (
            <p>No more blogs to load.</p> // Message when there are no more posts
          ) : (
            <Button
              className="border-btn large-btn"
              onClick={loadMorePosts} // Call loadMorePosts on click
              disabled={loadingMore} // Disable button while loading
            >
              {loadingMore ? "Loading..." : "Read More"}
            </Button>
          )}
        </div>
      </Container>
      {/* )} */}
    </>
  );
};

export default BlogList;
