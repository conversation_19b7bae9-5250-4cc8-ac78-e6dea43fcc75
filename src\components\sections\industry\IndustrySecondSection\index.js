"use client";;
import Image from "next/image";
import { Col, Container, Row } from "react-bootstrap";

const IndustrySecondSection = ({ title, image, description, routeType , type, imageItem }) => {
  // const imageRef = useRef(null);
  // const bgRef = useRef(null);
  // const animRef = useRef(null);
  // const dotRef = useRef(null);
  // const svgPathRef = useRef(null);

  // const [lineWidth, setLineWidth] = useState(1204);
  // const [pathLength, setPathLength] = useState(0);
  // const [dashOffset, setDashOffset] = useState(0);

  // useEffect(() => {
  //   const updateBgWidth = () => {
  //     if (imageRef.current && bgRef.current && animRef.current) {
  //       const imagePosition = imageRef.current.getBoundingClientRect();
  //       const distanceFromLeft = imagePosition.left;
  //       const distanceFromRight = imagePosition.right;

  //       const bgWidth = window.innerWidth - (distanceFromLeft + 150);
  //       bgRef.current.style.width = `${distanceFromLeft + 150}px`;
  //       bgRef.current.style.right = `${bgWidth}px`;
  //       const FloatingWidth =
  //         routeType === "hospital"
  //           ? 224
  //           : routeType === "shoppingmall"
  //           ? 121
  //           : 0;
  //       animRef.current.style.left = `${distanceFromRight - FloatingWidth}px`;

  //       const updatedLineWidth = animRef.current.getBoundingClientRect().width;
  //       setLineWidth(updatedLineWidth);
  //     }
  //   };

  //   updateBgWidth();
  //   window.addEventListener("resize", updateBgWidth);

  //   return () => {
  //     window.removeEventListener("resize", updateBgWidth);
  //   };
  // }, [routeType]);

  // useEffect(() => {
  //   const path = svgPathRef.current;
  //   const dot = dotRef.current;

  //   if (path && dot) {
  //     const totalLength = path.getTotalLength();
  //     setPathLength(totalLength);
  //     const updateDotPosition = () => {
  //       const maxScrollY = document.body.scrollHeight - window.innerHeight;
  //       const scrollProgress = Math.min(window.scrollY / maxScrollY, 1);
  //       const currentLength = scrollProgress * 5 * totalLength;
  //       const point = path.getPointAtLength(currentLength);

  //       dot.style.transform = `translate(${point.x - dot.offsetWidth / 2}px, ${
  //         point.y - dot.offsetHeight / 2
  //       }px)`;

  //       setDashOffset(totalLength - currentLength);
  //     };

  //     window.addEventListener("scroll", updateDotPosition);
  //     updateDotPosition();

  //     return () => {
  //       window.removeEventListener("scroll", updateDotPosition);
  //     };
  //   }
  // }, []);

  return (
    <section className={`sc-sec ${type}`}>
      <Container fluid={"xl"}>
        <Row className="align-items-lg-start align-items-center justify-content-start">
          <Col xl={5} lg={5} xs={12}>
            <div className="img-wrap d-lg-none d-flex">
              <Image
                src={imageItem}
                alt="banner-image"
                width="452"
                height="368"
                style={{ width: "100%", height: "auto" }}
              />
            </div>
          </Col>
          <Col lg={7} xs={12}>
            <div className="solution-details mt-2 ">
              <h2 className="main-title">{title}</h2>
              <p className="mb-0">{description}</p>
            </div>
          </Col>
        </Row>
      </Container>
      {/* <div ref={bgRef} className="bg-im-s"></div>
      <div className="bg-lst-prt"></div>

      <div
        ref={animRef}
        className={`bg-anim-ort ${
          routeType === "shoppingmall"
            ? "bg-shoppingmall"
            : routeType === "hospital"
            ? "bg-hospital"
            : ""
        }`}
        style={{
          // top:
          //   routeType === "hospital"
          //     ? "56px"
          //     : routeType === "shoppingmall"
          //     ? "105px"
          //     : undefined,
          zIndex:
            routeType === "hospital"
              ? 9
              : routeType === "shoppingmall"
              ? 0
              : undefined,
        }}
      >
        <animated.div ref={dotRef} className="navigation-point" />
        <div></div>
        {routeType === "hospital" && (
          <svg
            width="auto"
            height="277"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              // ref={svgPathRef}
              d={`M ${lineWidth} 4.5L885 4H542.5L469 4.5H357.5C346.5 4.4999 343 7.5 340.5 13C338 18.5 338.6 66.5 339 74.5C339.55 85.5 333.001 92.5 328.5 94.5C328.5 94.5 326 96.5 318 96.5C310.001 96.5 306 100 306 100`}
              stroke="#E3E3E3"
              strokeWidth="6"
            />
            <path
              ref={svgPathRef}
              d={`M ${lineWidth} 4.5L885 4H542.5L469 4.5H357.5C346.5 4.4999 343 7.5 340.5 13C338 18.5 338.6 66.5 339 74.5C339.55 85.5 333.001 92.5 328.5 94.5C328.5 94.5 326 96.5 318 96.5C310.001 96.5 306 100 306 100`}
              strokeWidth="6"
              stroke="#E3E3E3"
            />
            <path
              d={`M ${lineWidth} 4.5L885 4H542.5L469 4.5H357.5C346.5 4.4999 343 7.5 340.5 13C338 18.5 338.6 66.5 339 74.5C339.55 85.5 333.001 92.5 328.5 94.5C328.5 94.5 326 96.5 318 96.5C310.001 96.5 306 100 306 100`}
              strokeWidth="6"
              stroke="url(#paint0_linear_859_3514)"
              strokeDasharray={`${pathLength} ${pathLength}`}
              strokeDashoffset={dashOffset}
            />

            <defs>
              <linearGradient
                id="paint0_linear_859_3514"
                x1="3"
                y1="49.5"
                x2="714"
                y2="49.5"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#34A853" />
                <stop offset="1" stop-color="#06FF49" />
              </linearGradient>
            </defs>
            <path
              d="M -4 72 L 11 83 L 47 111 C 53 114 63 128 50 139 L 27.6851 154.347 C 17.0907 161.596 15.9714 176.807 25.3906 185.528 L 26.3569 186.423 C 31.1469 190.858 37.8001 192.677 44.1801 191.294 L 55.6074 188.818 C 60.9659 187.657 66.5661 188.747 71.0983 191.833 L 109.215 217.784 C 110.401 218.592 111.672 219.27 113.004 219.806 L 149.199 234.373 C 151.056 235.12 152.79 236.142 154.344 237.404 L 168 248.5 L 186.105 264.593 C 193.728 271.37 205.23 271.323 212.799 264.485 L 282.12 201.86 C 285.596 198.72 287.859 194.459 288.516 189.821 L 300.146 107.608 C 301.155 100.476 307.927 95.6397 315 97 V 97"
              stroke="#E3E3E3"
              strokeWidth="6"
            />
          </svg>
        )}
        {routeType === "shoppingmall" && (
          <svg
            width="auto"
            height="157"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3 153L72.6458 103C73.9074 102.312 72.7299 99.4261 69.7022 96.7827C66.6746 94.1392 72.1458 93 75 89"
              stroke="#E3E3E3"
              strokeWidth="7"
            />
            <path
              d={`M 70 95 C 72 93.3333 79.55 85.5 79 74.5 C 78.6 66.5 78 18.5 80.5 13 C 83 7.5 86.5 4.4999 97.5 4.5 H 209 L 282.5 4 H 625 H ${lineWidth}`}
              stroke="#E3E3E3"
              ref={svgPathRef}
              strokeWidth="7"
            />
            <path
              d={`M 70 95 C 72 93.3333 79.55 85.5 79 74.5 C 78.6 66.5 78 18.5 80.5 13 C 83 7.5 86.5 4.4999 97.5 4.5 H 209 L 282.5 4 H 625 H ${lineWidth}`}
              stroke="url(#paint0_linear_859_3514)"
              strokeWidth="7"
              strokeDasharray={`${pathLength} ${pathLength}`}
              strokeDashoffset={dashOffset}
            />
            <defs>
              <linearGradient
                id="paint0_linear_859_3514"
                x1="3"
                y1="49.5"
                x2="714"
                y2="49.5"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#34A853" />
                <stop offset="1" stop-color="#06FF49" />
              </linearGradient>
            </defs>
            <path
              d="M0 154.501L0.5 149.5L5.48484 156.152L4 157.001L0 154.501Z"
              fill="#E3E3E3"
            />
          </svg>
        )}
        {routeType === "events" && (
          <svg
            width="auto"
            height="100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2 5H739C747.284 5 754 11.7157 754 20V80C754 88.2843 760.716 95 769 95H1147C1155.28 95 1162 88.2843 1162 80V20C1162 11.7157 1168.72 5 1177 5H1920"
              stroke="white"
              strokeWidth="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        )}
      </div>

      <Container fluid={'xl'}>
        <Row className="align-items-lg-start align-items-center gx-lg-5 justify-content-start">
          <Col xl={4} lg={5} md={5} xs={12} ref={imageRef}>
            <div className="img-wrap">
              <Image
                src={image.src}
                alt="banner-image"
                width="452"
                height="368"
                style={{ width: "100%", height: "auto" }}
              />
            </div>
          </Col>
          <Col lg={7} md={7} xs={12}>
            <div className="solution-details mt-2 mt-md-5">
              <h2 className="main-title">{title}</h2>
              <p className="mb-0">{description}</p>
            </div>
          </Col>
        </Row>
      </Container> */}
    </section>
  );
};

export default IndustrySecondSection;
