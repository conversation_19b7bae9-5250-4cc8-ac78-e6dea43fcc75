"use client";;
import { useState } from "react";
import { <PERSON><PERSON>, Col, Container, Row } from "react-bootstrap";
import { TalkToModal } from "@/components/ui";

const SolutionBanner = ({ bgImage, title, description,source }) => {
  const [showModal, setShowModal] = useState(false);

  const openModal = () => {
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  return (
    <section
      className="industry-banner"
      style={{
        backgroundImage: `url(${bgImage?.src})`,
      }}
    >
      <Container>
        <Row className="align-items-center">
          <Col md={6} className="w-1024-100">
            <div className="industry-banner-details mb-xl-5">
              <h2
                className="main-title mb-3"
                dangerouslySetInnerHTML={{ __html: title }}
              ></h2>
              <p dangerouslySetInnerHTML={{ __html: description }} />
              <div className="btn-wrap mt-4">
                {/* eslint-disable react/no-unescaped-entities */}
                <Button
                  className="btn-secondary me-3"
                  onClick={() => openModal()}
                >
                  Let's talk to Sales
                </Button>
                {/* eslint-enable react/no-unescaped-entities */}
                {/* <Button className="btn btn-outline">See Demo</Button> */}
              </div>
            </div>
          </Col>
        </Row>
      </Container>
      <TalkToModal isOpen={showModal} onClose={() => closeModal()} hitUrl={source} />
    </section>
  );
};

export default SolutionBanner;
