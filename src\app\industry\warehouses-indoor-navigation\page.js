import IndustryBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryEBook from "../_components/IndustryEBook";
import IndustryHelp from "../_components/IndustryHelp";
import HeroImg from "@/assets/images/industry/warehouse-hero.png";
import SecImg3 from "@/assets/images/industry/event-help-illu.png";
import { TalkToOurTeam } from "@/components/sections";
import BgImg from "@/assets/images/industry/warehouse-accordion-bg.png";
import FaqSection from "../_components/FaqSection";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

// export const metadata = {
//   metadataBase: new URL("https://becomap.com"),
//   title: "Event Mapping and Wayfinding Solution | Becomap",
//   description:
//     "Discover how Event Mapping and Wayfinding solutions enhance healthcare environments for better navigation and patient experience at Becomap.",
//   images: ["/becomap.png"],
//   openGraph: {
//     title: "Event Mapping and Wayfinding Solution | Becomap",
//     description:
//       "Discover how Event Mapping and Wayfinding solutions enhance healthcare environments for better navigation and patient experience at Becomap.",
//     images: ["/becomap.png"],
//   },
// };

const eventNavigationFAQ = [
  {
    question: "Is this solution customizable to fit our warehouse’s unique layout?",
    answer: "Definitely. The system can be customized to reflect your warehouse’s specific zones, layout, and branding, making it a seamless part of your operations."
  },
  {
    question: "How does indoor navigation technology differ from GPS in a warehouse setting?",
    answer: "Unlike GPS, which struggles indoors, indoor navigation uses technologies like Bluetooth, Wi-Fi, or UWB to provide accurate positioning and navigation within warehouse environments."
  },
  {
    question: "Can indoor navigation help with reducing operational costs?",
    answer: "Absolutely. By minimizing search times, optimizing workflows, and reducing errors, the system significantly lowers labor and operational costs."
  },
  {
    question: "What is required to implement indoor navigation in my warehouse?",
    answer: "Implementation typically involves installing Bluetooth beacons, Wi-Fi access points, or UWB tags, along with software integration with your existing systems."
  }
];

const SOLUTIONS = [
  {
    title: "Clear Directions",
    description:
      "beComap’s Indoor navigation offers clear, easy-to-follow directions for patients and visitors to find their way through the hospital. From rooms to pharmacies, visitors can navigate smoothly, reducing confusion and saving time.",
    iconUrl: "/images/icons/patient.png",
    color: "#00BEDA",
  },
  {
    title: "Make Essential Services Easy to Find",
    description:
      "In a busy hospital, services like emergency rooms, labs, and pharmacies are crucial. beComap lets you highlight these key areas so patients can reach them without delay. No more guessing, just clear directions to the care they need.",
    iconUrl: "/images/icons/coins.png",
    color: "#ED55B9",
  },
  {
    title: "Use Data to Improve Efficiency",
    description:
      "Understanding how patients and visitors move through your hospital can help you optimize operations. beComap’s data insights allow you to monitor foot traffic, improve your layout, and keep things running smoothly—all without intruding on user privacy.",
    iconUrl: "/images/icons/management.png",
    color: "#ED790F",
  },
];

const accordionData = [
  {
    title: "Warehouse Navigation Made Simple",
    content:
      "beComap provides clear, turn-by-turn directions to key areas like restrooms, storage, and loading docks, helping employees find locations quickly and work more efficiently.",
  },
  {
    title: "Find Warehouse Resources Easily",
    content:
      "Employees can access a complete list of services and equipment, allowing them to stay informed and work productively.",
  },
  {
    title: "Integrate with Existing Systems",
    content:
      "beComap integrates seamlessly with your warehouse management software, accessible via mobile devices, tablets, or kiosks without additional downloads.",
  },
  {
    title: "Multi-Language Support",
    content:
      "Offer written directions in multiple languages, ensuring all employees can access directions and instructions in their preferred language.",
  },
  {
    title: "Stay Updated with Instant Alerts",
    content:
      "Getting timely updates on safety, inventory, or important announcements is essential. beComap ensures your team stays informed by sending real-time notifications directly to their devices. This immediate access to crucial information helps your staff respond quickly to changes, keeping the warehouse running smoothly and preventing any potential delays.",
  },
  {
    title: "Built to Fit Your Warehouse",
    content:
      "Every warehouse has its own way of doing things, and beComap is built to match your specific needs. The system can be adjusted to reflect your warehouse’s layout and branding, from adding custom colors to creating maps that reflect different zones within your space. This personalized approach ensures that beComap becomes a natural part of your daily operations, providing employees with a tool that’s designed to fit right in.",
  },
];

const ServiceData = [
  {
    title: "QR Code Scanning. No Downloads Needed",
    content:
      "beComap’s web-based solution means there are no app downloads required. Patients and visitors simply scan a QR code at the entrance to access step-by-step directions, making navigation effortless without additional hassle.",
    icon: "/images/icons/rfid.png",
    image: "/images/qr-code-scanning-health.png",
  },
  {
    title: "Parking Made Simple",
    content:
      "Finding a parking spot shouldn’t be another headache for visitors. beComap organizes hospital parking into zones with QR codes. Visitors scan the code when they park and are guided back to their vehicle when it’s time to leave, saving them from wandering around the lot.",
    icon: "/images/icons/parked-car.png",
    image: "/images/parking-made-simple.png",
  },
  {
    title: "Optimize Hospital Operations",
    content:
      "Managing a hospital involves a lot of moving parts. beComap helps you track equipment, manage space, and assign tasks efficiently, all from one simple dashboard. You’ll have everything you need to make your hospital run like a well-organized system, keeping both patients and staff happy.",
    icon: "/images/icons/pharmacy.png",
    image: "/images/optimize-hospital-operations.png",
  },
  {
    title: "Tailored Maps for Your Hospital’s Branding",
    content:
      "Customize your hospital’s map to reflect its branding with beComap’s flexible design options. From logos to color schemes, beComap ensures your hospital map is visually consistent with your identity while enhancing the patient experience.",
    icon: "/images/icons/map01.png",
    image: "/images/tailored-maps.png",
  },
];

// const [lineWidth, setLineWidth] = useState(1204);

// useEffect(() => {
//   const updateBgWidth = () => {
//     if (animRef.current) {

//       bgRef.current.style.width = `${distanceFromLeft + 150}px`;
//       const FloatingWidth =
//         routeType === "hospital"
//           ? 224
//           : routeType === "shoppingmall"
//           ? 111
//           : 0;
//       animRef.current.style.left = `${distanceFromRight - FloatingWidth}px`;

//       const updatedLineWidth = animRef.current.getBoundingClientRect().width;
//       setLineWidth(updatedLineWidth);
//     }
//   };

//   updateBgWidth();

//   window.addEventListener("resize", updateBgWidth);

//   return () => {
//     window.removeEventListener("resize", updateBgWidth);
//   };
// }, []);

const Warehouses = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <IndustryBanner
        source={"warehouses"}
        bgImage={HeroImg}
        title={`beComap’s Indoor Navigation & Mapping for Warehouses`}
        description="Make your warehouse a well-organized space where your team moves smoothly and gets more done, faster."
      />
      <IndustryClients />
      <section
        className="section bg-bc-light "
        style={{ position: "relative" }}
      >
        <div className="company-value-head mb-2">
          <h2 className="main-title">
            Clear Paths, Faster Operations, Improve Efficiency
          </h2>
          <p>
            {`Struggling to find items in a large warehouse? It’s a common issue for employees. Whether they’re searching for inventory or navigating to a specific location, beComap’s Indoor Navigation provides step-by-step directions throughout your warehouse. From the loading dock to storage areas, our system helps your employees to get to where they need to go, helping create a more organized and efficient environment.In a busy warehouse, wasted time can lead to operational delays. Our solution offers a simple way to streamline movement, guiding staff to inventory, workstations, and equipment with precision. Reducing time spent searching, leading to improved productivity for your team`}
          </p>
        </div>
        {/* <div
          ref={animRef}
          // className="bg-anim-ort"
        >
          <svg
            width="auto"
            height="100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2 5H739C747.284 5 754 11.7157 754 20V80C754 88.2843 760.716 95 769 95H1147C1155.28 95 1162 88.2843 1162 80V20C1162 11.7157 1168.72 5 1177 5H1920"
              stroke="white"
              strokeWidth="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div> */}
        <div className="event-sec">
          <Container fluid={"xl"}>
            <Row>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#00BEDA" }}
                    >
                      <Image
                        src={"/images/icons/location-1.png"}
                        alt={"Clear Navigation"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">Inventory Locator</h4>
                  </div>
                  <p className="">
                    Employees can find items quickly with accurate data on
                    inventory locations, minimizing search time.
                  </p>
                </div>
              </Col>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5 mt-lg-5 mt-4">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#ED55B9" }}
                    >
                      <Image
                        src={"/images/icons/split.png"}
                        alt={"Clear Navigation"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">Optimized Routing</h4>
                  </div>
                  {/* eslint-disable react/no-unescaped-entities */}
                  <p className="mb-0">
                    Provides the most efficient paths through the warehouse,
                    saving time and increasing throughput.
                  </p>
                </div>
              </Col>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5 mt-lg-0 mt-4">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#ED790F" }}
                    >
                      <Image
                        src={"/images/icons/folder.png"}
                        alt={"Clear Navigation"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">
                      Data Insights for Better Management
                    </h4>
                  </div>
                  <p className="mb-0">
                    Use data to track movement and optimize warehouse layout for
                    enhanced operational efficiency.
                  </p>
                </div>
              </Col>
              {/* eslint-enable react/no-unescaped-entities */}
            </Row>
          </Container>
        </div>
        {/* <svg
          width="auto"
          height="74"
          // viewBox="0 0 1920 74"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1941 5H1240.14C1231.85 5 1225.14 11.7157 1225.14 20V54C1225.14 62.2843 1218.42 69 1210.14 69H-21"
            stroke="white"
            strokeOpacity="0.5"
            strokeWidth="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg> */}
      </section>
      <IndustryAccordion
        title={"Data-Driven Warehouse Management, No More Warehouse Maze"}
        description={
          "Large warehouses can often feel like a maze, especially for new employees. beComap makes navigating easy, offering clear, step-by-step instructions to help staff find their way around without frustration.  Track how employees move through your warehouse to make informed decisions about optimizing layout and operations. With data insights from beComap, you can make adjustments to improve workflows, reduce downtime, and optimize overall efficiency, all while protecting privacy."
        }
        bgImage={BgImg}
        accordionData={accordionData}
        videoSrc={"/videos/mall.mp4"}
      />
      <IndustryHelp
        title="Why Leading Warehouses Trust beComap"
        description="beComap transforms the way you think about warehouse navigation. Our platform simplifies movement within large facilities, improves operational efficiency, and helps optimize workflows, making your warehouse a more productive environment."
        image={SecImg3}
        points={[
          "Best-in-class indoor mapping for large warehouses",
          "8 years of active R&D in location intelligence",
          "Optimized for use on mobile devices, tablets, and kiosks",
          "Easily integrates with existing warehouse management systems",
          "Web-based navigation without any downloads",
          "Supports multiple languages for diverse workforces",
        ]}
      />

      <IndustryEBook
        image={"/images/event-ebook-bg.png"}
        title={"Logistics In Action"}
        description={`<p>Unlock indoor navigation potential with our eBook. </p> <p>Explore technologies, strategies, and best practices to enhance wayfinding, visitor experience, and operations.</p>`}
      />

      <TalkToOurTeam source={"warehouses"}/>
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={eventNavigationFAQ}
      />
    </div>
  );
};

export default Warehouses;
