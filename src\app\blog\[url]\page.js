import { getCarouselBlog } from "@/lib/actions";
import BlogDetails from "@/components/sections/blog/blogDetails";

export async function generateStaticParams() {
  try {
    const posts = await getCarouselBlog(1, 10000);

    const slug = posts.results.map((post) => ({
      url: post.url,
    }));

    return slug;
  } catch (error) {
    console.error("Error fetching posts:", error);
    throw error;
  }
}

const SinglePage = ({ params }) => {
  const { url } = params;

  // Validate URL parameter to prevent injection attacks
  const sanitizedUrl = url?.replace(/[^a-zA-Z0-9-_]/g, '') || '';

  return (
    <main className="blog-post mt-5" itemScope itemType="https://schema.org/Article">
      <BlogDetails slug={sanitizedUrl} />
    </main>
  );
};

export default SinglePage;
