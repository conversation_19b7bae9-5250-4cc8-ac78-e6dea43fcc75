
"use client";;
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/swiper-bundle.css'; // Import Swiper bundle CSS
import 'swiper/css/effect-cards'; // Import effect-cards effect CSS
import Image from 'next/image';
import { EffectCards, Pagination } from 'swiper/modules';


const Testimonialslider = () => {
    const testimonialContent = [
        {
          writer: "Nashik City Centre",
          position: "Nashik, Maharashtra",
          content: "beComap helped improve our mall’s navigation. Their indoor navigation & Mall directory software made it easy for visitors to find stores and promotions, transforming the overall experience.",
          imgUrl: "/images/city-center-mall-nashik.png"
        },
        {
          writer: "Global Village",
          position: "Dubai",
          content: "Introducing be<PERSON><PERSON><PERSON>'s GPS navigation at Global Village has been a success. The indoor navigation ensures a smooth experience. Visitors now explore with ease, boosting participation and engagement in events and promotions.",
          imgUrl: "/images/global-village.png"
        },
        {
          writer: "Forum Mall",
          position: "Kochi, Kerala",
          content: "beCo's kiosk software has significantly improved the shopping experience at Forum Mall, Kochi. Shoppers easily access offers, stay updated on events, and provide feedback, boosting customer satisfaction.",
          imgUrl: "/images/forum-mall.jpg"
        },
        {
          writer: "HiLite Mall",
          position: "Kozhikode, Kerala",
          content: "To improve convenience and interactivity for our customers, we introduced indoor navigation. Partnering with beCo has been a great decision, we're pleased to see our customers enjoying the improved shopping experience.",
          imgUrl: "/images/hilite-mall.jpg"
        }    
      ];

    return (
        <div className="client-feedback-slider-wrap">
            <Swiper
                effect={'cards'}
                grabCursor={true}
                className="mySwiper"
                modules={[EffectCards, Pagination]}
                cardsEffect={{
                    rotate: false,
                    perSlideOffsetX: 10,

                }}
                pagination={{
                    clickable: true,
                    // dynamicBullets: true,
                }}
            >
                {testimonialContent.map((item) => (
                    <SwiperSlide key={item.content}>
                        <div className='testimonial-content-wrap '>
                       
                            <div className='testimonial-cont p-relative'>
                            <div className='quotes-img'>
                                <Image
                                    src="/images/icons/quotes-img.png"
                                    alt="quotes"
                                    width="65"
                                    height="52"
                                />
                            </div>
                                {item.content}
                            </div>
                            <div className='testimonial-footer d-flex align-items-center'>
                                <div className='auth-img'>
                                    <img
                                        src={item.imgUrl}
                                        alt={item.writer}
                                        
                                    />
                                </div>
                                <div className='auth-details'>
                                    <h5>{item.writer}</h5>
                                    <p>{item.position}</p>
                                </div>
                            </div>
                        </div>

                    </SwiperSlide>
                ))}
            </Swiper>
        </div>
    );
};

export default Testimonialslider;
