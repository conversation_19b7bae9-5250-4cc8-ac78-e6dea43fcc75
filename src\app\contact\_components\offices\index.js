"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "react-bootstrap";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css"; // Leaflet styles
import L from "leaflet";
import Image from "next/image";

// Fix leaflet marker icons not showing correctly
if (typeof window !== "undefined") {
  delete L.Icon.Default.prototype._getIconUrl;
  L.Icon.Default.mergeOptions({
    iconRetinaUrl:
      "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png",
    iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
    shadowUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png",
  });
}

const OfficeAddress = () => {
  // Coordinates for USA and India offices
  const usaCoordinates = [38.5045, -122.469];
  const indiaCoordinates = [10.0159, 76.3623];

  return (
    <div className="contact-section contact-office-wrap">
      <Container className="contact-container px-lg-0 px-4" fluid={"lg"}>
        <div className="section-head text-center mb-lg-5 mb-4">
          <h2 className="main-title">Offices</h2>
          <p>
            Discover the power of indoor positioning and tracking - contact us
            today!
          </p>
        </div>
        <Row>
          <Col md={6} className="mb-md-0 mb-5">
            <div className="office-img-wrap mb-30">
              <MapContainer
                zoomControl={false}
                center={usaCoordinates}
                zoom={20}
                style={{ height: "400px", width: "100%", borderRadius: "30px" }}
              >
                <TileLayer
                  url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
                  attribution='&copy; <a href="https://carto.com/">CartoDB</a> contributors'
                />

                <Marker position={usaCoordinates}>
                  <Popup>
                    USA Office: 1335 Main St. Suite 108, St. Helena, California
                    94574, U.S.A.
                  </Popup>
                </Marker>
              </MapContainer>
            </div>
            <div className="office-details-wrap">
              <h3>USA</h3>
              <div className="address-cont">
                1335 Main St. Suite 108,<br /> St. Helena, 
                California 94574,<br /> U.S.A
              </div>
              <div className="d-flex address-cont">
                <span>
                  <Image
                    src="/images/call-img.png"
                    alt="phone"
                    width="23"
                    height="23"
                  />
                </span>
                ****** 457 0620
              </div>
            </div>
          </Col>

          <Col md={6}>
            <div className="office-img-wrap mb-30">
              <MapContainer
                center={indiaCoordinates}
                zoomControl={false}
                zoom={20}
                style={{ height: "400px", width: "100%", borderRadius: "30px" }}
              >
                <TileLayer
                  url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
                  attribution='&copy; <a href="https://carto.com/">CartoDB</a> contributors'
                />

                <Marker position={indiaCoordinates}>
                  <Popup>
                    India Office: CWS#12, Room No. 17, Second Floor, Geo Info
                    Park, Kakkanad, Kochi-682042.
                  </Popup>
                </Marker>
              </MapContainer>
            </div>
            <div className="office-details-wrap">
              <h3>India</h3>
              <div className="address-cont">
                CWS#12, Room No. 17, Second Floor, <br />
                Geo Info Park, Kakkanad,
                <br />
                Kochi-682042
              </div>
              <div className="d-flex address-cont">
                <span>
                  <Image
                    src="/images/call-img.png"
                    alt="phone"
                    width="23"
                    height="23"
                  />
                </span>
                +91 99469 22769
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default OfficeAddress;
