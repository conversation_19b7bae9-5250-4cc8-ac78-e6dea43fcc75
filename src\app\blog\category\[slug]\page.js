import { getCarouselBlog } from "@/lib/actions";
import CategoryList from "@/components/sections/blog/categoryList/categoryList";
import { TalkToOurTeam } from "@/components/sections";

export async function generateStaticParams() {
  try {
    const posts = await getCarouselBlog(1, 10000);

    const slug = posts.results.map((post) => ({
      slug: post.category.slug,
    }));

    return slug;
  } catch (error) {
    console.error("Error fetching posts:", error);
    throw error;
  }
}


const Blog = ({ params }) => {
  const { slug } = params;

  // Validate slug parameter to prevent injection attacks
  const sanitizedSlug = slug?.replace(/[^a-zA-Z0-9-_]/g, '') || '';

  return (
    <div className="blog-page-wrap">
      <CategoryList slug={sanitizedSlug} />
      <div>
        <TalkToOurTeam source={`blog/${sanitizedSlug}`} />
      </div>
    </div>
  );
};

export default Blog;
