.client-feedback-slider-wrap {
    @media (max-width:991px){
        width: 100%;
        margin-bottom: 50px;
    }
    .swiper {
        width: 552px;
        height: 630px;

        @media (max-width:1199px){
            width: 430px!important;
        }
        @media (max-width:991px){
            width: 100%!important;
            max-width: 520px!important;
            height: 350px!important;
        }
        @media (max-width:424px){
            height: 450px!important;
        }
    }

    .swiper-slide {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #fff;
        background: linear-gradient(145.4deg, #34a853 1.7%, #34a853 97.56%);
        box-shadow: 0px 5px 20px rgba(2, 40, 39, 0.37);
        border-radius: 30px;
    }
 
    .auth-img {
            width: 74px;
            height: 74px;
            background-color: #fff;
            border-radius: 50%;
            border: 3px solid #BDBDBD;
            object-fit: contain;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            img{
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    .quotes-img {
        position: absolute;
        left: -30px;
        top: -90px;
    }
    .auth-details {
        padding-left: 20px;
        h5 {
            font-family: $font-bold;
            font-size: 20px;
            line-height: 26px;
            margin: 0;
        }
        p {
            font-family: $font-bold;
            font-size: 15px;
            line-height: 26px;
            margin: 0;
            color: #95ffb1;
        }
    }
    .swiper-pagination {
        background-color: #1d9e40;
        border-radius: 100px;
        display: flex;
        align-items: center;
        height: 30px;
        width: auto !important;
        position: absolute;
        left: 50% !important;
        transform: translate(-50%);
        justify-content: center;
        padding: 0 15px;
        bottom: 40px;
        .swiper-pagination-bullet {
            background-color: rgba(255, 255, 255, 0.49);
            &.swiper-pagination-bullet-active {
                background-color: rgba(255, 255, 255, 1);
            }
        }
    }
}

@media (max-width: 1400px) {
    .client-feedback-slider-wrap {
        .swiper {
            width: 482px;
            height: 550px;
        }
    }
}

@media (max-width: 767px) {
    .client-feedback-slider-wrap {
        width: 100%;
        margin-bottom: 50px;
        .swiper {
            width: 85%;
            height: 350px;
        }

        .testimonial-content-wrap {
            padding: 0 20px 0 20px;
        }
    }
    .service-itembox-img-wrap {
        img {
            max-width: 100% ;
            height: auto !important;
        }
    }
}