import Image from "next/image";
import { <PERSON><PERSON>, Col, Container, Row } from "react-bootstrap";

const IndustryEBook = ({ title, image, description }) => {
  return (
    <section
      className="e-book-sec"
      style={{
        marginBottom: "-4px",
        backgroundImage: `url(${image})`,
      }}
    >
      <Container>
        <Row className="align-items-center gx-5">
          <Col xs={5} className="ms-auto">
            <div className="ebook-details">
              <h2 className="main-title mb-3">{title}</h2>
              <div
                className="mb-0"
                dangerouslySetInnerHTML={{ __html: description }}
              />
              <Button className="btn-secondary mt-4">Download Ebook</Button>
            </div>
          </Col>
          <Col xs={6}>
            <div className="ebook-img-2">

              <Image
                src="/images/ebook-1.png"
                alt="ebook"
                width="399"
                height="325"
              />
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default IndustryEBook;
