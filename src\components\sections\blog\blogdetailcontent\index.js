import BlogSubscribe from '../subscribe';
import DOMPurify from 'isomorphic-dompurify';




const BlogDetailContent = ({ content }) => {


 

  // Function to create nested TOC structure
 

  

  const renderContentWithSubscription = () => {
    const paragraphs = content.split(/<\/p>/).map((para, index) => {
      // Sanitize HTML content to prevent XSS attacks
      const sanitizedContent = DOMPurify.sanitize(para + '</p>');
      return (
        <div key={index}>
          <div dangerouslySetInnerHTML={{ __html: sanitizedContent }} />
          {index === 0 && <BlogSubscribe />}
        </div>
      );
    });

    return paragraphs;
  };

  return (
    <div className='blog-detail-content'>
      {renderContentWithSubscription()}
    </div>
  );
};

export default BlogDetailContent;
