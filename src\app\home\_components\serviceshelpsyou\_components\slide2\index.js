import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

const ServicesHelpsSlider2 = () => {
  const router = useRouter();
  return (
    <div className="srv-helpslide-box-mid d-flex">
      <div className="srv-helpslide-imgbox srv-helpslide-imgbox1 d-flex flex-md-column justify-content-center">
        <h4 className="text-end">Hotels & Resorts</h4>
        <div className="helpyou-overlay-wrap">
          <div className="helpyou-overlay-item">
            <span>
              <button className="btn-secondary btn btn-primary" onClick={() => router.push("/industry/hotels-resorts-indoor-navigation")}>Read More</button>
            </span>
          </div>
          <Image
            src="/images/hotels.png"
            alt="Service help"
            width={0}
            height={0}
            style={{ width: "100%", height: "100%" }}
          />
        </div>
      </div>
      <div className="srv-helpslide-imgbox srv-helpslide-imgbox2 d-flex flex-md-column justify-content-start">
        <div className="helpyou-overlay-wrap">
          <div className="helpyou-overlay-item">
            <span>
              <button className="btn-secondary btn btn-primary" onClick={() => router.push("/industry/schools-universities-indoor-navigation")}>Read More</button>
            </span>
          </div>
          <Image
            src="/images/schools.png"
            alt="Service help"
            width={0}
            height={0}
            style={{ width: "100%", height: "100%" }}
          />
        </div>
        <h4>Schools & Universities</h4>
      </div>
      <div className="srv-helpslide-imgbox srv-helpslide-imgbox3 d-flex flex-md-column justify-content-center">
        <h4>Warehouses</h4>
        <div className="helpyou-overlay-wrap">
          <div className="helpyou-overlay-item">
            <span>
              <button className="btn-secondary btn btn-primary" onClick={() => router.push("/industry/warehouses-indoor-navigation")}>Read More</button>
            </span>
          </div>
          <Image
            src="/images/warehouse.png"
            alt="Service help"
            width={0}
            height={0}
            style={{ width: "100%", height: "100%" }}
          />
        </div>
      </div>
      <div className="srv-helpslide-imgbox srv-helpslide-imgbox4 d-flex flex-md-column justify-content-end">
        <h4>Events & Trade Shows</h4>
        <div className="helpyou-overlay-wrap">
          <div className="helpyou-overlay-item">
            <span>
              <button className="btn-secondary btn btn-primary" onClick={() => router.push("/industry/events-indoor-navigation")}>Read More</button>
            </span>
          </div>
          <Image
            src="/images/events.png"
            alt="Service help"
            width={0}
            height={0}
            style={{ width: "100%", height: "100%" }}
          />
        </div>
      </div>
    </div>
  );
};

export default ServicesHelpsSlider2;
