import Skeleton from "react-loading-skeleton";
import 'react-loading-skeleton/dist/skeleton.css'


const BlogLoader = ({ imageHeight }) => {
  return (
    <div
      className="blogpost"
      
    >
      <div
        className="blogpost-ins"
      >
        <div className="blog-postimage-wrap">
          <Skeleton height={imageHeight ? imageHeight : "585"} style={{borderRadius: "20px"}}/>
        </div>
      
        <div className="blog-post-description">
          <h1 className="blogpost-heading mb-1"><Skeleton count={1.5} /></h1>
          <p className="blogpost-paragraph"><Skeleton count={3.5} height={10} /></p>
        </div>
      </div>
    </div>
  );
};

export default BlogLoader;
