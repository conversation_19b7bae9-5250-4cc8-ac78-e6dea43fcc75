import SolutionBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
import IndustrySolutions from "../_components/solutions";
import FaqSection from "../_components/FaqSection";
import SolutionWhyUs from "../_components/solutionWhyUs";
import HeroImg from "@/assets/images/solutions/proximity-hero.png";
import SecImg from "@/assets/images/solutions/becomap-care.png";
import { TalkToOurTeam } from "@/components/sections";
import Blogs from "@/app/home/<USER>/blogs";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

import SolutionWhyUsBg from "@/assets/images/solutions/why-bg.png";
import WhySubImage from "@/assets/images/solutions/proximity/why-sub-img.png";
import SolutionsServices from "../_components/IndustryServices";


export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: "Best Proximity Service |  Becomap",
  description:
    "Explore Becomap's Proximity Services for tailored customer engagement. Enhance experiences with precise location-based solutions. Learn more today!",
  images: ["/becomap.png"],
  openGraph: {
    title: "Best Proximity Service |  Becomap",
    description:
      "Explore Becomap's Proximity Services for tailored customer engagement. Enhance experiences with precise location-based solutions. Learn more today!",
    images: ["/becomap.png"],
  },
};

const mallNavigationFAQ = [
  {
    question: "What are the key features of proximity services?",
    answer: "Proximity services offer features like location-based push notifications, targeted marketing, personalized promotions, and seamless customer interaction within a specific geographic area."
  },
  {
    question: "Are proximity services secure for handling customer data?",
    answer: "Yes, our proximity services use encryption and secure protocols to protect customer data, ensuring compliance with privacy regulations and safeguarding sensitive information."
  },
  {
    question: "How do you maintain and update proximity services?",
    answer: "We offer ongoing support and maintenance, including regular software updates, hardware checks, and performance monitoring to ensure your proximity services remain efficient and secure."
  }
];

const SOLUTIONS = [
  {
    title: "Place Beacons at Key Locations",
    description:
      "Bluetooth beacons are positioned at important touchpoints, such as hospital departments, airport gates, event stages, and service desks. This ensures that visitors receive notifications at moments when they need relevant information the most.",
    iconUrl: "/images/icons/light.png",
    color: "#34A853",
  },
  {
    title: "Trigger Notifications Based on Location",
    description:
      "As visitors pass by a beacon, the Web App sends a message to their device. This can include flight updates, appointment reminders, event schedules, or information about nearby services, encouraging them to take action or explore further.",
    iconUrl: "/images/icons/wifi.png",
    color: "#34A853",
  },
  {
    title: "Increase Engagement & Improve Venue Exploration",
    description:
      "Notifications capture visitors' attention, encouraging them to access services, attend events, or explore new areas. By directing patients, reminding attendees of activities, or helping travelers find amenities, these prompts highlight key areas, guide visitors to underutilized spaces, and enhance their overall experience.",
    iconUrl: "/images/icons/location-2.png",
    color: "#34A853",
  },
];

const accordionData = [
  {
    title: "Easily Integrate Maps",
    content:
      "Integrate map of your retail space into existing apps to effortlessly guide shoppers to their favorite stores, restaurants, and services via the shortest route.",
  },
  {
    title: "Multi-Language Support",
    content:
      "Make your mall map accessible to all visitors by offering it in multiple languages. beComap allows you to provide directions and information in the shopper’s preferred language, making the mall experience more personalized and user-friendly. This feature is particularly valuable in diverse or tourist-heavy locations.",
  },
  {
    title: "Give Updates on Store Offers",
    content:
      "Keep shoppers in the loop with instant updates on store deals, upcoming events, and essential services, improving their overall shopping experience.",
  },
  {
    title: "Less Searching, More Shopping",
    content:
      "Navigate in a flash with our system—find the fastest route and shortest path from parking to stores, or multi-stop routes to plan your shopping with less walking and more items on your cart.",
  },
];

const ServiceData = [
  {
    title: "1. Proximity Marketing",
    content:
      "With beComap, you can deliver personalized offers and promotions directly to users based on their location. This helps increase customer loyalty and drive foot traffic by providing relevant information when visitors are closest to your stores or services.",
    image: "/images/proximity-marketing.png",
  },
  {
    title: "2. Crisis Management",
    content:
      "In an emergency or unexpected event, beComap’s proximity services help you keep visitors informed and safe. You can send location-based alerts, guide users to safe zones, and manage the situation more effectively by keeping everyone in control.",
    image: "/images/crisis-management.png",
  },
  {
    title: "3. Location-Based Notifications",
    content:
      "beComap’s system allows you to send updates and alerts to users based on their exact location in your venue. From sending promotional messages to delivering critical updates, you can engage users with timely information to enhance their overall experience.",
    image: "/images/location-based-notifications.png",
  },
];

const WHY_DATA = [
  {
    title: "Timely Relevant Information",
    description:
      "Visitors receive notifications exactly when they need them, such as flight updates at an airport, event reminders at a venue, or appointment alerts in a hospital – improving their overall experience.",
    iconUrl: "/images/icons/website.png",
    activeIconUrl: "/images/icons/website-active.png",
    color: "#34A853",
  },
  {
    title: "Smart Navigation for Visitors",
    description:
      "Beacons effectively guide visitors to underused areas like quiet lounges and alternative service desks, improving traffic distribution. By directing people to less busy entrances and open service areas, they reduce congestion and boost the overall flow throughout the venue, ensuring a more efficient use of space and services.",
    iconUrl: "/images/icons/find-location.png",
    activeIconUrl: "/images/icons/find-location-active.png",
    color: "#34A853",
  },
  {
    title: "Improved Visitor Engagement ",
    description:
      "Notifications about nearby services, activities, or promotions capture attention, leading to more interactions, event participation, and use of available amenities.",
    iconUrl: "/images/icons/map-loc.png",
    activeIconUrl: "/images/icons/map-loc-active.png",
    color: "#34A853",
  }  
];

const ProximityServices = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <SolutionBanner
        source={"proximity-services"}
        bgImage={HeroImg}
        title="Engage & Connect with Our Proximity Services"
        description="beComap’s Proximity Services provides data on user engagement, helping businesses communicate with visitors at the right place and time. Whether it’s marketing, alerts, or resource management, our solutions help you connect with users meaningfully—exactly when they need it, all without collecting any personal details from your visitors."
      />
      <IndustryClients />
      <SolutionWhyUs
        title={"What Are Proximity Services?"}
        description={
          "beComap’s Proximity Services allow you to engage visitors as they move through your venue, using location-based alerts and notifications to deliver the right message at the right moment. By leveraging advanced location analytics, you can interact with visitors when they are near the beacon, whether it’s delivering promotions, managing crowds, or sending timely updates."
        }
        bgImage={SolutionWhyUsBg}
        subImage={WhySubImage}
        whyData={WHY_DATA}
      />
      <IndustrySolutions
        image={SecImg}
        solutions={SOLUTIONS}
        title={"How Bluetooth Beacons Drive Proximity Engagement?"}
        description={{
          part1:
            "Bluetooth beacons create meaningful connections between visitors and their surroundings by delivering timely notifications as they move through your venue. Placed at strategic locations like entrances, key service areas, or event spaces, these beacons send relevant updates directly to visitors' devices. Whether it’s guiding travelers to a gate, ",
          part2:
            "notifying patients about appointments, or alerting attendees about an event, this approach drives interaction and encourages visitors to explore spaces they might have otherwise overlooked, resulting in better engagement across the venue.",
          part3:
            "This personalized approach not only improves the overall visitor experience but also drives business outcomes. By leveraging Bluetooth beacons, venues can increase foot traffic to underutilized areas, promote targeted offers, and streamline operations. ",
        }}
      />

      {/* 
      <IndustrySecondSection
        image={SecImg4}
        routeType="shoppingmall"
        title={"No More Mazes. Get Shoppers Straight to the stores."}
        description="Lost customers and missed sales opportunities are things of the past.  beComap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With beComap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustrySolutions
        imageProperty={{
          width: 441,
          height: 505,
          top: "-120px",
          bottom: "0",
          justifyContentParent: "end",
          justifyContent: "end",
          backgroundColor: "gradient",
        }}
        image={SecImg}
        solutions={SOLUTIONS}
        title={"Mall Experience like never before"}
        description="Lost customers and missed sales opportunities are things of the past.  beComap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With beComap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustryExperience
        image={SecImg2}
        title="Mall Navigation Made Easy"
        description="Our solution makes navigating your mall simple and stress-free. It offers detailed info on essential services like restrooms, ATMs, and customer service desks through the app. Shoppers can use easy turn-by-turn directions to find stores and services quickly. By enhancing their overall experience and reducing frustration, more visitors will be encouraged to spend time in our mall, increasing foot traffic and driving sales."
      />
      <IndustryAccordion
        title={"Discover 3X More Stores!"}
        bgImage={BgImg}
        description={
          "With beComap, shoppers get a complete list of all the stores in your mall, including detailed information and current offers. Now, they can explore more shops than ever before and find the best deals, turning every visit into an exciting shopping adventure and driving more foot traffic to your mall."
        }
        accordionData={accordionData}
        videoSrc={"/videos/mall.mp4"}
      />
    
      <IndustryHelp
        title="Why World’s largest venues trust beComap"
        description="beComap changes the way you think about retail navigation & engagement. Our platform simplifies navigation, attracts and retains customers, and optimizes operations to improve the overall shopping journey."
        image={SecImg3}
        points={[
          "Best-in-class indoor mapping for malls",
          "8 years on active R&D in location intelligence",
          "Optimized for use on mobile devices, web browsers, and kiosks",
          "Easy integration with existing apps",
          "Web-based navigation without any downloads",
          "Multiple language support",
        ]}
      />
 */}

      <SolutionsServices
        title="What Makes Proximity Services Effective?"
        description="Proximity services allow you to engage with users in a more personalized way by sending targeted messages based on their exact location. Whether you’re promoting a sale or delivering important updates, you can interact with visitors at the right time and in the right place, making those interactions more meaningful. Visitors can easily navigate through the space, receive personalized offers, and stay informed, making their experience more engaging and convenient."
        image={"/images/kiosk-dummy.png"}
        serviceData={ServiceData}
      />
       <section
        className="section primary-bg"
        style={{
          backgroundImage: `url("/images/solution-bg.png")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          minHeight: "680px",
        }}
      >
        <Container>
          <Row className="align-items-center">
            <Col lg={7}>
              <Image
                src={"/images/solution-sec.png"}
                alt="indoor mapping software"
                width={550}
                height={450}
                style={{ width: "100%", height: "auto" }}
              />
            </Col>
            <Col lg={5}>
              <div className="section-head">
                <span
                  className="mb-4"
                  style={{
                    fontWeight: 600,
                    fontSize: "18px",
                  }}
                >
                  Case Study
                </span>
                <h2 className="main-title mb-3">Indoor mapping software</h2>
                <p>
                  Develop an ultra-modern travel environment that ree
                  experiences. Set new standards in travel convenience and
                  satisfaction through innovation, sustainability, and
                  operational efficiency.
                </p>
              </div>
              <button className="btn btn-dark mt-4">Learn More</button>
            </Col>
          </Row>
        </Container>
      </section>
      {/* <IndustryEBook
        image={"/images/mall-ebook-bg.png"}
        title={"Retail In Action"}
        description={`<p>According to ACI, global passenger traffic will reach 9.7 billion this year.</p> <p>Learn how to deliver a smooth travel experience with indoor maps in our helpful retail guide.</p>`}
      /> */}
      <Blogs />
      <TalkToOurTeam source={"proximity"} />
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={mallNavigationFAQ}
      />
    </div>
  );
};

export default ProximityServices;
